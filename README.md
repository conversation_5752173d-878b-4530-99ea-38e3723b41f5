# 2025 最新 uni-app 开发模板

uni-app Vue3 + Vite5 + TypeScript + Pinia + Unocss
使用最新 uni-app vue-cli 模板， vue3、vite 最新版本搭建，通过命令方式运行和打包构建，加入了 unocss 原子化开发方式，极大的提升您的开发效率。

此项目需要在 H5/微信小程序/支付宝小程序/滴滴H5 中运行，语法请按照 uni-app 官方文档进行开发

## 技术架构

基于 vue3+vite+uniapp+typescript+unocss+nutui-uniapp

## UI 组件库

nutui-uniapp[https://nutui-uniapp.pages.dev/guide/quick-start]


## 项目注意事项
- 引入其他三方依赖请联系开发
- pages/didi 这里写首页的所有内容（api请求这些，页面，资源，样式 ）
- pagesCommon 这里写其他页面的所有内容（api请求这些，页面，资源，样式 ）
- 其他目录如有需要引入的可以直接引入，主项目也有对应的文件
- 这样做的目的是方便双方开发，以及代码方便合并到主仓库

## 运行命令

- 普通H5

```bash
pnpm  run test:h5
```

- 滴滴H5

```bash
pnpm run test:h5-didi
```

## 项目开发注意事项

1. 由于此项目是在 H5/微信小程序/支付宝小程序/滴滴H5 中运行，注意在小程序中本地图片的引用方式不同，请使用相对路径 例如

   ```vue
   <image :src="'../../static/icon/back.svg'"></image>
   ```

2. 统一导航页面 #layouts/CommonPageLayout
3. 公共组件 #components
4. 项目中的所有组件尽量使用 Nutui-uniapp 中的组件，色值使用unocs中默认配置的色值，或者使用theme.scss中的色值,
   由于滴滴的色值不同，所以在打包的时候 会更换相关的色系
