<!doctype html>
<html lang="zh" build-date="%BUILD_DATE%">
  <head>
    <meta charset="UTF-8" />
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon" />
    <script>
      // 设置新的安全区域值
      // document.documentElement.style.setProperty("--safe-area-inset-top", "44px")
    </script>
    <script type="module">
      if (import.meta.env.MODE !== "production") {
        const script = document.createElement("script")
        script.src = "https://unpkg.com/vconsole@latest/dist/vconsole.min.js"
        document.head.appendChild(script)
        script.onload = function () {
          new window.VConsole()
        }
      } else {
        const style = document.createElement("style")
        style.innerHTML = `
    * {
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -khtml-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
  `
        document.head.appendChild(style)
      }
    </script>
    <script>
      var coverSupport =
        "CSS" in window &&
        typeof CSS.supports === "function" &&
        (CSS.supports("top: env(a)") || CSS.supports("top: constant(a)"))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
          (coverSupport ? ", viewport-fit=cover" : "") +
          '" />',
      )
    </script>
    <title></title>
    <style>
      body,
      html {
        background-color: #f7f9fc;
        --nut-navbar-title-font: 34rpx;
      }

      html,
      body {
        overflow: hidden; /* 确保没有滚动条显示 */
        position: fixed; /* 确保页面内容可以滚动 */
        width: 100%;
        height: 100%;
        overscroll-behavior: none;
      }

      .menu-checked {
      }

      .menu-unchecked {
        color: #333333 !important;
      }

      body {
        overflow-y: scroll; /* 允许页面滚动但不显示滚动条 */
        -webkit-overflow-scrolling: touch; /* 确保在 iOS 中有惯性滚动效果 */
      }

      body::-webkit-scrollbar {
        display: none; /* 针对 WebKit 内核隐藏滚动条 */
      }

      :root {
        --safe-area-inset-top: 44px; /* 默认值 */
      }

      .uni-toast {
        border-radius: 0px !important;
      }

      .nut-navbar--safe-area-inset-top {
        height: 180rpx !important;
        padding-top: 88rpx !important;
      }
    </style>
    <!--preload-links-->
    <!--app-context-->
  </head>

  <body>
    <div id="app"><!--app-html--></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
