import path from "node:path"
import dayjs from "dayjs"
import { defineConfig, loadEnv } from "vite"
import Uni from "@dcloudio/vite-plugin-uni"
import UniLayouts from "@uni-helper/vite-plugin-uni-layouts"
import UniPlatform from "@uni-helper/vite-plugin-uni-platform"
import UniManifest from "@uni-helper/vite-plugin-uni-manifest"
import UnoCSS from "unocss/vite"
import AutoImport from "unplugin-auto-import/vite"
import { visualizer } from "rollup-plugin-visualizer"

import ViteRestart from "vite-plugin-restart"
import { resolve } from "path"

export default ({ command, mode }) => {
  console.log("command, mode -> ", command, mode)
  const isMp = process.env?.UNI_PLATFORM?.startsWith("mp") ?? false

  const { UNI_PLATFORM } = process.env.UNI_PLATFORM
  const isDIDI = process.env.UNI_CUSTOM_SCRIPT === "h5-didi"
  console.log("UNI_PLATFORM -> ", isDIDI) // 得到 mp-weixin, h5, app 等

  const env = loadEnv(mode, path.resolve(process.cwd(), "env"))
  const {
    VITE_APP_PORT,
    VITE_SERVER_BASEURL,
    VITE_DELETE_CONSOLE,
    VITE_SHOW_SOURCEMAP,
    VITE_APP_PROXY,
    VITE_APP_PROXY_PREFIX,
  } = env
  console.log("环境变量 env -> ", env)
  console.log("环境变量 env ->2222 ", resolve(__dirname, "./src/static"))

  return defineConfig({
    envDir: "./env", // 自定义env目录
    base: command !== "serve" ? "./" : "/",
    plugins: [
      UniLayouts(),
      UniPlatform(),
      UniManifest(),
      Uni(),
      {
        name: "fix-vite-plugin-vue",
        configResolved(config) {
          const plugin = config.plugins.find((p) => p.name === "vite:vue")
          if (plugin && plugin.api && plugin.api.options) {
            plugin.api.options.devToolsEnabled = false
          }
        },
      },
      UnoCSS({
        // 明确指定配置文件路径
        configFile: "uno.config.ts",
      }),
      AutoImport({
        imports: ["vue", "uni-app"],
        dts: "src/types/auto-import.d.ts",
        dirs: ["src/hooks"], // 自动导入 hooks
        eslintrc: { enabled: true },
        vueTemplate: true, // default false
      }),

      ViteRestart({
        // 通过这个插件，在修改vite.config.js文件则不需要重新运行也生效配置
        restart: ["vite.config.js"],
      }),
      // h5环境增加编译时间
      !isMp && {
        name: "html-transform",
        transformIndexHtml(html) {
          return html.replace("%BUILD_DATE%", dayjs().format("YYYY-MM-DD HH:mm:ss"))
        },
      },
      // 打包分析插件，h5 + 生产环境才弹出
      // UNI_PLATFORM === "h5" &&
      //   mode === "production" &&
      //   visualizer({
      //     filename: "./node_modules/.cache/visualizer/stats.html",
      //     open: true,
      //     gzipSize: true,
      //     brotliSize: true,
      //   }),
    ],
    define: {
      __UNI_PLATFORM__: JSON.stringify(UNI_PLATFORM),
      __VITE_APP_PROXY__: JSON.stringify(VITE_APP_PROXY),
      "process.env.NODE_ENV": mode,
      "process.env.DEV": command === "serve",
    },
    css: {
      postcss: {
        plugins: [],
      },
      preprocessorOptions: {
        scss: {
          additionalData: !isDIDI
            ? "@import '@/style/theme.scss';@import 'nutui-uniapp/styles/variables.scss';"
            : "@import '@/style/theme-didi.scss';@import 'nutui-uniapp/styles/variables.scss';",
        },
      },
    },

    resolve: {
      alias: {
        "@": resolve(__dirname, "./src"),
        "@/components": resolve(__dirname, "./src/components"),
        "@/static": resolve(__dirname, "./src/static"),
        "@/style": resolve(__dirname, "./src/style"),
        "@/common": resolve(__dirname, "./src/common"),
      },
    },
    server: {
      host: true, // 启动后浏览器窗口输入地址就可以进行访问
      port: 4444, // 端口号
      cors: true,
      strictPort: false, // 是否是严格的端口号，如果true，端口号被占用的情况下，vite会退出
      hmr: true, // 开启热更新
      proxy: {
        "https://api-gw.dev.huangbaoche.com/capp/": {
          // 配置接口调用目标地址
          target: "http://api-gw.dev.huangbaoche.com/capp/",
          // 当进行代理时，Host 的源默认会保留（即Host是浏览器发过来的host），如果将changeOrigin设置为true，则host会变成target的值。
          changeOrigin: true,
          // 前缀 /api 是否被替换为特定目标，不过大多数后端给到的接口都是以/api打头，这个没有完全固定的答案，根据自己的业务需求进行调整
          rewrite: (path) => path.replace("https://api-gw.dev.huangbaoche.com/capp/", ""),
        },
        "https://api-gw.test.huangbaoche.com/capp/": {
          // 配置接口调用目标地址
          target: "http://api-gw.test.huangbaoche.com/capp/",
          // 当进行代理时，Host 的源默认会保留（即Host是浏览器发过来的host），如果将changeOrigin设置为true，则host会变成target的值。
          changeOrigin: true,
          // 前缀 /api 是否被替换为特定目标，不过大多数后端给到的接口都是以/api打头，这个没有完全固定的答案，根据自己的业务需求进行调整
          rewrite: (path) => path.replace("https://api-gw.test.huangbaoche.com/capp/", ""),
        },
        "https://fr-internal-new.oss-cn-hangzhou.aliyuncs.com/": {
          // 配置接口调用目标地址
          target: "https://fr-internal-new.oss-cn-hangzhou.aliyuncs.com/",
          // 当进行代理时，Host 的源默认会保留（即Host是浏览器发过来的host），如果将changeOrigin设置为true，则host会变成target的值。
          changeOrigin: true,
          // 前缀 /api 是否被替换为特定目标，不过大多数后端给到的接口都是以/api打头，这个没有完全固定的答案，根据自己的业务需求进行调整
          rewrite: (path) =>
            path.replace("https://fr-internal-new.oss-cn-hangzhou.aliyuncs.com/", ""),
        },
      },
    },
    build: {
      sourcemap: command === "serve", // 默认是false
      target: "es2015",
      // 开发环境不用压缩
      minify: mode !== "production" ? "terser" : "terser",

      rollupOptions: {
        // 方法1：使用 external 排除特定库，这些库将不会被打包

        output: {
          // 方法二：使用 manualChunks 控制代码分割和库的处理

          chunkFileNames: (chunkInfo) => {
            return "js/[name]-[hash].js"
          },
          entryFileNames: (chunkInfo) => {
            return "js/[name]-[hash].js"
          },
          assetFileNames: "[ext]/[name]-[hash].[ext]",
          minifyInternalExports: true,
        },
      },
      terserOptions: {
        compress: {
          drop_console: mode === "production",
          drop_debugger: true,
        },
      },
    },
  })
}
