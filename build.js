const fs = require("fs")
const { baseParamsHK, baseParams } = require("./build/base")

const compressing = require("compressing")
const createHttpService = require("./build/request")
const path = require("path")
const args = process.argv.splice(2)

const h5Id = "10000005"
const env = args[0]
const target = args[1]
console.log("当前打包环境==》》》》》》》", env, "目标服务器", target)

const envPlatform = path.resolve(__dirname, "./src/utils/env-platform.json")
// 读取envPlatform
const envPlatformJson = fs.readFileSync(envPlatform)
const envPlatformJsonObj = JSON.parse(envPlatformJson)
console.log(
  `-------------------  envPlatformJson:${JSON.stringify(envPlatformJsonObj)}   -------------------`,
)
fs.writeFileSync(
  envPlatform,
  JSON.stringify({
    ...envPlatformJsonObj,
    env,
    target,
    flavor: "c_app",
  }),
)

// -----------------------准备完成，开始上传---------------------------------
const httpService = createHttpService("https://hbc-mpaas-manager.dev.huangbaoche.com/")

let ossUrl = ""
const appSetting = baseParams[env]

compressing.zip
  .compressDir(`./dist/${env}/${h5Id}`, `./dist/${env}/${h5Id}.zip`)
  .then(async (res) => {
    try {
      console.log("压缩完成,开始上传")
      const token = await getMcubeFileToken(h5Id)
      console.log("token", token)
      // 2.传文件到oss
      await uploadOss(token, `./dist/${env}/${h5Id}.zip`, `${h5Id}.zip`)
      ossUrl = token.host + "/" + token.dir + `${h5Id}.zip`
      await getNewAppVersion(h5Id)
    } catch (e) {
      console.log(`上传失败开始上传h5 到服务器${JSON.stringify(e)}`)
      // copyDirectory(`./dist/${env}/${h5Id}`, "./dist")
      // deleteFiles(`./dist/${env}`)
    }
  })
console.log("压缩成功💐💐💐💐💐💐💐💐💐")

async function getMcubeFileToken(appId) {
  const response = await httpService.get(`${target}/getMcubeFileToken`, {
    ...appSetting,
    onexFlag: true,
  })
  if (response.getFileTokenResult.success) {
    return response.getFileTokenResult.fileToken
  } else {
    throw new Error("获取token失败")
  }
}

async function uploadOss(params, zip, zipName) {
  console.log("zip", zip, zipName)
  const FormData = require("form-data")
  const data = new FormData()
  const fs = require("fs")
  const axios = require("axios")
  data.append("policy", params.policy)
  data.append("signature", params.signature)
  data.append("OSSAccessKeyId", params.accessid)
  data.append("key", params.dir + zipName)
  data.append("file", fs.createReadStream(zip))
  const config = {
    method: "post",
    url: params.host,
    timeout: 10000,
    headers: {
      Accept: "*/*",
      ...data.getHeaders(),
    },
    data,
  }
  return axios(config)
}

async function getNewAppVersion(appId) {
  const response = await httpService.get(`${target}/listMcubeNebulaResources`, {
    h5Id: appId,
    ...appSetting,
  })
  if (response.listMcubeNebulaResourceResult && response.listMcubeNebulaResourceResult.success) {
    const oldApp = response.listMcubeNebulaResourceResult.nebulaResourceInfo[0]
    await uploadZip(appId, oldApp)
  } else {
    throw new Error("获取app信息失败")
  }
}

async function uploadZip(appId, oldApp) {
  const params = {
    fileUrl: ossUrl,
    h5Version: incrementVersion(oldApp.h5Version),
    mainUrl: oldApp.mainUrl,
    h5Id: appId,
    h5Name: oldApp.h5Name,
    vhost: oldApp.vhost,
    extendInfo: oldApp.extendInfo,
    autoInstall: oldApp.autoInstall,
    resourceType: oldApp.resourceType,
    installType: oldApp.installType,
    platform: oldApp.platform,
    clientVersionMin: oldApp.clientVersionMin,
    clientVersionMax: oldApp.clientVersionMax,
    repeatNebula: 0,
    onexFlag: true,
    ...appSetting,
  }
  console.log("开始上传zip包", params)

  const response = await httpService.get(`${target}/createMcubeNebulaResource`, params)
  if (
    response.createMcubeNebulaResourceReslult &&
    response.createMcubeNebulaResourceReslult.success
  ) {
    console.log("上传zip包成功🚀🚀🚀🚀🚀🚀🚀🚀🚀🚀", appId, env, response)
    if (env !== "production") {
      await publishTask(response.createMcubeNebulaResourceReslult.nebulaResourceId)
    }
    // copyDirectory(`./dist/${env}/${h5Id}`, "./dist")
    // deleteFiles(`./dist/${env}`)
  } else {
    throw new Error(`上传zip包失败${JSON.stringify(response)}`)
    // copyDirectory(`./dist/${env}/${h5Id}`, "./dist")
    // deleteFiles(`./dist/${env}`)
  }
}

async function publishTask(nebulaResourceId) {
  try {
    const params = {
      publishType: 3,
      packageId: nebulaResourceId,
      id: 0,
      ...appSetting,
    }
    // const  ListMcubeNebulaTasksResponse = await httpService.get("listMcubeNebulaResources", {
    //   h5Id:appId,
    //   ...appSetting
    // });
    const axiosResponse = await httpService.get(`${target}/createMcubeNebulaTask`, params)
    console.log("创建发布任务成功", axiosResponse)
  } catch (e) {
    throw new Error(`创建发布任务失败${JSON.stringify(e)}`)
  }
}

function incrementVersion(version) {
  const parts = version.split(".").map(Number)
  parts[parts.length - 1]++
  const newVersion = parts.join(".")
  return newVersion
}

function deleteFiles(folderPath) {
  if (fs.existsSync(folderPath)) {
    fs.readdirSync(folderPath).forEach((file) => {
      const curPath = path.join(folderPath, file)

      if (fs.lstatSync(curPath).isDirectory()) {
        // 递归删除子文件夹
        deleteFiles(curPath)
      } else {
        // 删除文件
        fs.unlinkSync(curPath)
      }
    })

    // 删除空文件夹
    fs.rmdirSync(folderPath)
  } else {
  }
}

function copyDirectory(sourceDir, targetDir) {
  // 开始发布适配微信小程序的网站
  // 创建目标文件夹
  fs.mkdirSync(targetDir, { recursive: true })

  // 读取源文件夹中的内容
  const files = fs.readdirSync(sourceDir)

  // 遍历源文件夹中的每个文件或子目录
  files.forEach((file) => {
    const sourcePath = path.join(sourceDir, file)
    const targetPath = path.join(targetDir, file)
    // 检查文件的类型
    const stats = fs.statSync(sourcePath)
    if (stats.isFile()) {
      // 如果是文件，则直接复制
      fs.copyFileSync(sourcePath, targetPath)
    } else if (stats.isDirectory()) {
      // 如果是目录，则递归调用复制目录函数
      copyDirectory(sourcePath, targetPath)
    }
  })
  // 然后删除dist目录
}
