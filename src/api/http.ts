/* eslint-disable-next-line */
/* prettier-ignore */

import Request from "luch-request";
import { IRequest } from "@/api/types"
import { isAliPay } from "@/utils/is"
import { getHbcUserInfo, goLogin, postNotification, Ready } from "@/utils/mpaas/alipayUtil"
import { getAppAk } from "@/api/common"
import { StorageKey } from "@/utils/storage"

/**
 * 鉴权失败回到登录页面
 */
const handleAuthFailure = () => {
  // const userStore = useUserStore()
  // userStore.clearUserInfo()
  // uni.removeStorageSync("userInfo")
  // uni.navigateTo({ url: "/pages/login/index" })
  if (isAliPay()) {
    postNotification("LOGIN_EXPIRATION", {})
    goLogin()
  } else {
    // 其登陆
  }
}

const cclxApiKey = () => {
  // #ifdef H5
  return import.meta.env.VITE_API_KEY_MPALIPAY
  // #endif

  // #ifdef MP-WEIXIN
  return import.meta.env.VITE_API_KEY_MPWEIXIN
  // #endif
}

const HEADER = {
  // #ifdef MP-WEIXIN
  flavor: "c_wxmn",
  // #endif
  // #ifdef H5
  flavor: "c_app",
  // #endif
  lang: "zh",
  "X-CCLX-Api-Key": cclxApiKey(),
  // ak: "a221858018fcdcf8e5131227ca80b211",
  // ut: "23d1aa96efe8bec9ce0ca1bd4e8a1e9e",
  ak: "bff9c6ff841318bc3d51a15d8225d8ad",
  ut: "048e5a1fcca3d4b95c6d4bc4433daebc",
  nv: "",
  mv: "",
}
const options = {
  baseURL: import.meta.env.VITE_BASE_URL,
  header: HEADER,
}
const request = new Request(options)

function getUt(userInfo: any) {
  // #ifdef MP-WEIXIN
  try {
    const token = uni.getStorageSync(StorageKey.USER_TOKEN)
    if (token) {
      return token
    }
    const token2 = userInfo
      ? userInfo?.userToken
        ? userInfo?.userToken || ""
        : (userInfo?.userInfo && JSON.parse(userInfo?.userInfo)?.userToken) || ""
      : ""
    uni.setStorageSync(StorageKey.USER_TOKEN, token2)
    return token2
  } catch (e) {
    return ""
  }

  // #endif

  // #ifdef H5
  return userInfo
    ? userInfo?.userToken
      ? userInfo?.userToken || ""
      : (userInfo?.userInfo && JSON.parse(userInfo?.userInfo)?.userToken) || ""
    : ""
  // #endif
}

/**
 * 请求拦截器
 */
request.interceptors.request.use(
  async (options) => {
    if (options.custom.loading) {
      if (isAliPay()) {
        Ready(() => {
          AlipayJSBridge.call("showLoading", {
            text: "加载中...",
          })
        })
      } else {
        await uni.showLoading({
          title: "加载中...",
        })
      }
    }
    if (isAliPay()) {
      const userInfo: any = await getHbcUserInfo()
      const HBC_AK: any = await getAppAk(options.url)
      options.header = {
        ...options.header,
        ak: HBC_AK,
        ut: getUt(userInfo),
      }
    }

    // #ifdef MP-WEIXIN
    const userInfo: any = await getHbcUserInfo()
    const HBC_AK: any = await getAppAk(options.url)
    options.header = {
      ...options.header,
      ak: HBC_AK,
      ut: getUt(userInfo),
    }
    // #endif
    return options
  },
  (options) => Promise.reject(options),
)
/**
 * 响应拦截器
 */
request.interceptors.response.use(
  async (response) => {
    if (response.config.custom.loading) {
      if (isAliPay()) {
        Ready(() => {
          AlipayJSBridge.call("hideLoading")
        })
      } else {
        uni.hideLoading()
      }
    }

    const { data } = response
    // 兼容上传文件功能
    // 上传文件，接口返回一个空
    if (data === "") return data

    const { status, message } = data
    if (status === 200) {
      return data as any
    }
    await uni.showToast({
      title: message,
      icon: "none",
    })
    const authFailureCodes = [10012, 10400]
    if (authFailureCodes.includes(status)) {
      handleAuthFailure()
      return Promise.reject(response)
    }
    if (status === 500) {
      return Promise.reject(response)
    }
    return Promise.reject(response)
  },
  (response: any) => {
    if (isAliPay()) {
      Ready(() => {
        AlipayJSBridge.call("hideLoading")
      })
    } else {
      uni.hideLoading()
    }
    uni.showToast({
      title: "服务器开了一个小差",
      icon: "none",
    })
    Promise.reject(response)
  },
)

export const post = <T = any>(params: IRequest) => {
  const { url, data = {}, loading = true, header = {} } = params
  return request.post<T>(url, data, {
    header,
    custom: {
      loading,
    },
  })
}
export const postForm = (params: IRequest) => {
  const { url, data = {}, loading = true } = params
  return request.post<any>(url, {}, {
    params: data,
    custom: {
      loading,
    },
  } as any)
}
export const get = <T = any>(params: IRequest) => {
  const { url, data = {}, loading = true } = params

  return request.get<T>(url, {
    params: data,
    custom: {
      loading,
    },
  } as any)
}

export const upload = async ({
  files,
  file,
  name,
  formData = {},
  loading,
  url,
}: {
  files?: File[]
  file?: File
  name?: string
  formData?: { [k: string]: any }
} & Pick<IRequest, "url" | "loading">) => {
  return request.upload(url, {
    files: files as any,
    file,
    name,
    formData,
    custom: {
      loading,
    },
  })
}
