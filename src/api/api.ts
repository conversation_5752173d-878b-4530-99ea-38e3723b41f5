import { get, post } from "@/api/http"

// 获取车辆信息--附加服务
export const getCarDetail = (params: any, loading: boolean = false) => {
  return get({
    url: "/capp-api/travelPlan/p/v1.0/getTravelPlanByNo",
    data: params,
    loading,
  })
}

// 获取期望的其它服务
export const getDescService = (params: any, loading: boolean = false) => {
  return get({
    url: "capp-api/bics/p/v1.0/hbcConfigMoreServiceTags/List",
    data: params,
    loading,
  })
}

// 获取优惠券
export const getCouponList = (params: any, loading: boolean = true) => {
  return post({
    url: "marketing-service/coupon/p/v4.0/getOrderCouponList",
    data: params,
    loading,
  })
}

// 获取订单详情
export const getOrderDetail = (params: any, loading: boolean = false) => {
  return get({
    url: "/capp-api/travel/c/v7.0/orderDetail",
    data: params,
    loading,
  })
}
// 评论向导
export const updateTravelComment = async (data: any, loading = false) => {
  return await post({
    url: `/capp-api/travel/c/v1.0/comment/updateTravelComment`,
    data,
    loading,
  })
}

/**
 * capp 查询整个行程单的评论
 * @param data 传参
 */
export const queryAllCommentByTravelNo = async (
  data: { orderTravelNo: string },
  loading = false,
) => {
  return await get({
    url: `/capp-api/travel/c/v1.0/comment/queryAllCommentByTravelNo`,
    data,
    loading: false,
  })
}
// 获取星级描述
export const getStarLevelDesc = () => {
  return post<{
    [k: string]: {
      id: string
      keywordGroup: string
      keywordGroupDescription: string
      starType: string
      sysOrgCode: string
      tagId: number
      tagInfo: string
      tagOrder: number
    }[]
  }>({
    url: "/capp-api/bics/p/v1.0/hbcConfigEvaluationStarTags/list",
    loading: false,
  })
}
export const getOrderRequirementApi = (params: any, loading: boolean = false) => {
  return get({
    url: "/capp-api/travel/c/v4.0/getOrderRequirement",
    data: params,
    loading,
  })
}

// 支付
export const payInfo = (params: any, loading: boolean = true) => {
  return post({
    url: "/pay-service/pay/c/v4.0/payInfo",
    data: params,
    loading,
  })
}

// 猜你想问
export const getFaqList = (params: any, loading: boolean = false) => {
  return get({
    url: "/capp-api/bics/p/v1.0/hbcConfigOrderQuestion/list",
    data: params,
    loading,
  })
}

// 查询发单状态
export const getDispatchOrderStatus = (params: any, loading: boolean = false) => {
  return get({
    url: "/capp-api/travel/c/v4.0/getDeliverInfo",
    data: params,
    loading,
  })
}

// 生成订单
export const orderCreate = (params: any, loading: boolean = true) => {
  return post({
    url: "/capp-api/travelPlan/p/v1.0/reserveToC",
    data: params,
    loading,
  })
}

export const classicDailyCarApi = (params: any, loading: boolean = true) => {
  return post({
    url: "/capp-api/travel/p/v6.0/classicDailyCar",
    data: params,
    loading,
  })
}

// 补差价
export const payDifference = (params: any, loading: boolean = true) => {
  return post({
    url: "/capp-api/travel/c/v1.0/diffOrder",
    data: params,
    loading,
  })
}

// 向导个人中心
export const getGuideCenter = (params: any, loading: boolean = false) => {
  return get({
    url: "/capp-api/query/c/v1.0/query/providerOrderInfo",
    data: params,
    loading,
  })
}

// 获取动态
export const getDynamic = (params: any, loading: boolean = false) => {
  return get({
    url: "/capp-api/query/c/v1.0/query/getProviderDynamicContentList",
    data: params,
    loading,
  })
}

// 获取评价
export const getEvaluate = (params: any, loading: boolean = true) => {
  return post({
    url: "/capp-api/hgs/goods/p/queryProviderEvaluation",
    data: params,
    loading,
  })
}

// 评价标签
export const queryProviderCommentTag = (params: any, loading: boolean = true) => {
  return post({
    url: "/capp-api/hgs/goods/p/queryProviderCommentTag",
    data: params,
    loading,
  })
}

// 订单详情
export const orderDetailApi = (params: any, loading: boolean = true) => {
  return get({
    url: "/capp-api/travel/c/v7.0/orderDetail",
    data: params,
    loading,
  })
}

/**
 * 发起支付
 */
export const startPayApi = (params: any, loading: boolean = true) => {
  return post({
    url: "/pay-service/pay/p/v4.0/payInfo",
    data: params,
    loading,
  })
}

/**
 * 小费支付
 */
export const startPayTipApi = (params: any, loading: boolean = true) => {
  return post({
    url: "/pay-service/pay/p/v4.0/tip/pay/info",
    data: params,
    loading,
  })
}

/**
 * 小费支付
 */
export const getCurrencyRateByCountryIdApi = (params: any, loading: boolean = true) => {
  return get({
    url: "/capp-api/basic/v1.0/currency/getCurrencyRateByCountryId",
    data: params,
    loading,
  })
}

/**
 * 取消订单选项
 */
export const listForSelectApi = (params: any, loading: boolean = true) => {
  return get({
    url: "capp-api/basic/basic/v2.0/cancelOrder/listForSelect",
    data: params,
    loading,
  })
}

/**
 * 北京时间
 */
export const bjTimeApi = (params: any, loading: boolean = false) => {
  return get({
    url: "capp-api/basic/p/v1.0/bjTime",
    data: params,
    loading,
  })
}

/**
 * 更新订单联系人
 */
export const updateOrderTravelContactApi = (params: any, loading: boolean = true) => {
  return post({
    url: "/order-travel-service/update/c/v4.0/updateOrderTravelContact",
    data: params,
    loading,
  })
}

/**
 * 目标信息
 */
export const targetInfoApi = (params: any, loading: boolean = true) => {
  return get({
    url: "/capp-api/im/v3.0/c/targetInfo",
    data: params,
    loading,
  })
}

/**
 * 用户客服单聊
 */
export const userCustomerServiceSingleChatApi = (params: any, loading: boolean = true) => {
  return get({
    url: "/capp-api/im/v3.0/c/v1.0/userCustomerServiceSingleChat",
    data: params,
    loading,
  })
}

/**
 * 静默包车  CApp预订详情取消规则 下单页
 */
export const getPolicyApi = (params: any, loading: boolean = true) => {
  return post({
    url: "/order-travel-service/query/c/v4.0/getPolicy",
    data: params,
    loading,
  })
}

export const getFeeDescApi = (params: any, loading: boolean = true) => {
  return post({
    url: "order-travel-service/query/c/v4.0/getFeeDesc",
    data: params,
    loading,
  })
}

/* 静默包车  汇率换算 */
export const exchangeRateConversion = (params: any, loading: boolean = true) => {
  return get({
    url: "capp-api/basic/v1.0/currency/getCurrencyRateByCountryId",
    data: params,
    loading,
  })
}

export const getRewardDefaultAmountForTravelNo = (params: any, loading: boolean = true) => {
  return get({
    url: "capp-api/search/p/v1.1/query/batchByProviderId",
    data: params,
    loading,
  })
}

export const queryLastTipPayApi = (params: any, loading: boolean = true) => {
  return get({
    url: "order-service/query/c/v4.0/queryLastTipPay",
    data: params,
    loading,
  })
}

export const orderInvoiceDetailApi = (params: any, loading: boolean = false) => {
  return get({
    url: "capp-api/invoice/c/v1.0/order/orderInvoiceDetail",
    data: params,
    loading,
  })
}

export const cancelOrderConfirmApi = (params: any, loading: boolean = true) => {
  return post({
    url: "order-travel-service/cancel/c/account/v4.0/cancelOrderConfirm",
    data: params,
    loading,
  })
}

export const deliverOuttownApi = (params: any, loading: boolean = false) => {
  return post({
    url: "/capp-api/purchase/c/v1/deliverOuttown",
    data: params,
    loading,
  })
}

export const getImStatusApi = (params: any, loading: boolean = false) => {
  return post({
    url: "customize-service/v1.0/getImStatus",
    data: params,
    loading,
  })
}

export const getAdditionalPriceApi = (params: any, loading: boolean = true) => {
  return get({
    url: "/capp-api/price/quoteprice/p/v1.0/getProviderMoreServiceByCityId",
    data: params,
    loading,
  })
}
