/// <reference types="vite/client" />
/// <reference types="vite-svg-loader" />

declare module "*.vue" {
  import { DefineComponent } from "vue"
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>
  export default component
}

interface ImportMetaEnv {
  /** 后台接口地址 */
  readonly VITE_BASE_URL: string
  readonly VITE_BASE_road_book_URL: string
  readonly VITE_API_KEY_MPALIPAY: string
  readonly VITE_API_KEY_MPWEIXIN: string
  readonly VITE_PROVIDER_H5: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
