// color

// 主色调
$primary-color: var(--nut-primary-color, #ff6435) !default;
$primary-color-end: var(--nut-primary-color-end, #ff6435) !default;

// 辅助色
$help-color: var(--nut-help-color, #f5f5f5) !default;

// 标题常规文字
$title-color: var(--nut-title-color, #1a1a1a) !default;

// 副标题
$title-color2: var(--nut-title-color2, #666) !default;

// 次内容
$text-color: var(--nut-text-color, #808080) !default;

// button
$button-large-height: var(--nut-button-large-height, 56px) !default;
$button-default-height: var(--nut-button-default-height, 44px) !default;
//分割线
$divider-line-height: var(--nut-divider-line-height, 0.5px);
// cell 去掉阴影
$cell-box-shadow: var(--nut-cell-box-shadow, 0 0 0 0);

// 步进器字体大小
$inputnumber-input-font-size: var(--nut-inputnumber-input-font-size, 14px) !default;
$inputnumber-input-background-color: var(--nut-inputnumber-input-background-color, none);
$inputnumber-input-width: var(--nut-inputnumber-input-width, 25rpx);
$divider-line-height: var(--nut-divider-line-height, 1px);
