import { createSSRApp } from "vue"
import App from "./App.vue"
import store from "./store"
import { prototypeInterceptor } from "./interceptors"
import "virtual:uno.css"
import CustomLoading from "@/components/loading/index.vue"
import CustomError from "@/components/error/index.vue"

export function createApp() {
  const app = createSSRApp(App)
  app.component("CustomLoading", CustomLoading)
  app.component("CustomError", CustomError)
  app.use(store)
  app.use(prototypeInterceptor)
  return {
    app,
  }
}
