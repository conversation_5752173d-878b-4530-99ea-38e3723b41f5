export class StorageSingleton {
  private static instance: StorageSingleton

  private userInfo: any = {}

  private constructor() {}

  public static getInstance(): StorageSingleton {
    if (!StorageSingleton.instance) {
      StorageSingleton.instance = new StorageSingleton()
    }
    return StorageSingleton.instance
  }

  public setItem(key: string, value: any): void {
    uni.setStorageSync(key, value)
  }

  public getItem(key: string): any {
    return uni.getStorageSync(key)
  }

  public removeItem(key: string): void {
    uni.removeStorageSync(key)
  }

  public clear(): void {
    uni.clearStorageSync()
  }

  public setUserInfo(userInfo: any): void {
    this.userInfo = userInfo
  }

  public getUserInfo(): any {
    return this.userInfo
  }
}

export const Constants = {
  // 本地存储的key
  STORAGE_KEY: {
    // 用户信息
    AK: "ak",
    UT: "ut",
    APP_LANGUAGE: "LANGUAGE",
  },
}

export async function clearStorage(isLogin = false) {}
