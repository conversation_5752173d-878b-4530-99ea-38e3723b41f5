export enum StorageKey {
  USER_INFO = "USER_INFO",
  USER_TOKEN = "USER_TOKEN",
  ACCESS_TOKEN = "ACCESS_TOKEN",
  USER_ROLE = "USER_ROLE",
  REGISTER_ID = "REGISTER_ID",
  BASE_URL = "BASE_URL",
  API_KEY = "API_KEY",
  COUNTRY_ID = "COUNTRY_ID",
  COUNTRY_INFO = "COUNTRY_INFO",
  IMTOKEN = "IM_TOKEN",
  TIMTOKEN = "TIM_TOKEN",
  IMID = "IM_ID",
  IMINFO = "IMINFO",
  KEY_EFFECT_STATUS = "KEY_EFFECT_STATUS",
  APP_VERSION = "APP_VERSION",
  mpaasVersion = "mpaasVersion",
}

const paraseUrl = (href: string) => {
  if (href.indexOf("?") == -1) {
    return {}
  }
  const params = href.split("?")[1]
  const paramsArr = params.split("&")
  try {
    const paramsObj: any = {}
    paramsArr.forEach((item) => {
      const key = item.split("=")[0]
      const value = item.split("=")[1]
      paramsObj[key] = value
    })
    return paramsObj
  } catch (e: any) {}
}

/**
 * 获取链接必传参数 ，ak,ut,
 */
export function getUrlParams() {
  // #ifdef H5
  return paraseUrl(window.location.href)
  // #endif

  // #ifdef MP-WEIXIN
  // 获取当前路径上的参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  console.log(currentPage.options, "currentPage")
  return currentPage.options
  // #endif
}
