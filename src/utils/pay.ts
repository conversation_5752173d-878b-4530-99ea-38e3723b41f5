import { getAPDataStoragePromise, getHbcUserInfo, popWindow } from "@/utils/mpaas/alipayUtil"
import { appAlipayPayGo, getHbcStorage } from "./mpaas/miniToNative"

/**
 * 生成支付宝支付请求参数。
 * @param {Object} params - 支付请求参数。
 * @param {string} params.orderNo - 订单号。
 * @param {number} params.payOrderCategory - 支付订单类别。
 * @param {number} params.price - 价格。
 * @param {string} params.userId - 用户ID。
 * @param {any} [params.payMethod] - 支付方式。
 * @returns {Object} 支付宝支付请求参数对象。
 */
export const generateAliPay = (params: any = {}) => {
  const { orderNo, payOrderCategory, price, userId, payMethod } = params as any
  console.log("支付前请求参数,", params)

  // 检查必要的参数是否存在
  if (!orderNo || !payOrderCategory || !price || !userId) {
    throw new Error("必要的支付参数缺失")
  }

  // 获取移动操作系统类型
  const payClient = getMobileOS()

  return {
    opId: userId,
    payClient,

    // #ifdef MP-WEIXIN
    payGateWay: 17,
    // #endif

    // #ifdef H5
    payGateWay: 18,
    // #endif

    orderInfoList: [
      {
        orderNo,
        payOrderCategory,
        price,
      },
    ],
    chinaUms: {},
    payMethod,
  }
}

/**
 * 支付宝支付
 * @param payInfo
 * @param param
 */
export const toPayMentAlipay = (param: any) => {
  console.log(`-------------------  param:${JSON.stringify(param)}   -------------------`)
  // 0000是支付成功
  appAlipayPayGo(param)
    .then((res: any) => {})
    .catch((err: any) => {
      // alert(JSON.stringify(err))
      if (err.code == 1003) {
        // uni.showToast({
        //   title: '请安装支付宝客户端',
        // })
      } else {
        // uni.showModal({
        //   title: '支付异常',
        //   content: err,
        // })
      }
    })
}

export interface AppWXPay {
  userId: string // userId
  orderNo: string // 订单号
  payNumber: any // 支付金额
  accessKey: string // accessKey
  userToken: string // userToken
  orderConfirmNo?: string // userToken
  payMethodType?: any // userToken
  tipPay?: any // userToken
  payOrderCategory?: any // userToken
}

/**
 * 微信支付
 * @param queryParmes
 */
export async function appWXPayGo(param = { orderNo: "", price: "" }) {
  const userInfo = await getHbcUserInfo()
  const HBC_AK = await getAPDataStoragePromise("HBC_AK")
  const userToken = userInfo
    ? userInfo?.userToken
      ? userInfo?.userToken || ""
      : JSON.parse(userInfo?.userInfo)?.userToken || ""
    : ""
  const appWXPayParmes: AppWXPay = {
    userId: userInfo?.userId || "",
    orderNo: param.orderNo,
    orderConfirmNo: param.orderConfirmNo,
    tipPay: param.tipPay,
    payNumber: param.price,
    accessKey: HBC_AK?.data,
    userToken,
    payMethodType: 1,
    payOrderCategory: param.diffTip == 1 ? 21 : 18,
  }
  const APPWXPAYGO_DEBUG = import.meta.env.VITE_APPWXPAYGO_DEBUG
  const params = {
    appid: "gh_5fb95a9f65e5",
    path: "pages/appPayMent/appPayMent",
    query: { ...appWXPayParmes },
    debug: APPWXPAYGO_DEBUG,
  }
  AlipayJSBridge.call("pay_mini_go", params, (result) => {
    if (result.code === "1") {
      const obj = JSON.parse(result.message)
    }
  })
}

function getMobileOS() {
  // #ifdef MP-WEIXIN

  switch (uni.getSystemInfoSync().platform) {
    case "android":
      return 1
    case "ios":
      return 2
    default:
      return 5
  }

  // #endif

  // #ifdef H5
  const userAgent = navigator.userAgent || navigator.vendor || window.opera
  if (/android/i.test(userAgent)) {
    return 1
  }
  if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
    return 2
  }
  return 5
  // #endif
}
