import dayjs from 'dayjs'

const joinZreo = (num: any) => {
    if (String(num).length === 1) {
        return '0' + num
    } else {
        return num
    }
}

/** 倒计时剩多少小时 */
export const getHourCountDown = (time: any) => {
    let nowTime = new Date()
    let hour = dayjs(time).diff(nowTime, 'hour')
    return hour
}

/** 倒计时剩多少分钟 */
export const getMinuteCountDown = (time: any) => {
    let nowTime = new Date()
    let minute = dayjs(time).diff(nowTime, 'minute')
    return minute
}

/** 倒计时剩多少秒 */
export const getSecondCountDown = (time: any) => {
    let nowTime = new Date()
    let second = dayjs(time).diff(nowTime, 'second')
    return second
}
/** 获取时间中的年份 */
export const getYear = (time: any) => {
    let month = joinZreo(dayjs(time).get('year'))
    return month
}
/** 获取时间中的月份 */
export const getMonth = (time: any) => {
    let month = joinZreo(dayjs(time).get('month') + 1)
    return month
}

/** 获取时间中的日 */
export const getDate = (time: any) => {
    let date = joinZreo(dayjs(time).get('date'))
    return date
}

/** 获取时间中的小时 */
export const getHour = (time: any) => {
    let hour = joinZreo(dayjs(time).get('hour'))
    return hour
}

/** 获取时间中的分钟 */
export const getMinute = (time: any) => {
    let minute = joinZreo(dayjs(time).get('minute'))
    return minute
}

/** 根据时间戳获取时间 */
export const getTimeForment = (millis: any) => {
    let time = dayjs.unix(millis).format('YYYY-MM-DD HH:mm:ss')
    return time
}

/** 获取时间中的月日小时分钟按 'XX月xx日 XX:XX' 的形式返回 */
export const getDateFormat = (time: any) => {
    let month = joinZreo(dayjs(time).get('month') + 1)
    let date = joinZreo(dayjs(time).get('date'))
    let hour = joinZreo(dayjs(time).get('hour'))
    let minute = joinZreo(dayjs(time).get('minute'))
    return `${month}月${date}日 ${hour}:${minute}`
}
/** 获取时间中的年月日小时分钟按 'XX.XX.XX XX:XX' 的形式返回 */
export const getDateFormatUnString = (time: any) => {
    let year = dayjs(time).get('year')
    let month = joinZreo(dayjs(time).get('month') + 1)
    let date = joinZreo(dayjs(time).get('date'))
    let hour = joinZreo(dayjs(time).get('hour'))
    let minute = joinZreo(dayjs(time).get('minute'))
    return `${year}.${month}.${date} ${hour}:${minute}`
}
/** 获取时间中的年月日小时分钟按 'XXXX年X月X日' 的形式返回 */
export const getDateFormatStringUnTime = (time: any) => {
    let year = dayjs(time).get('year')
    let month = dayjs(time).get('month') + 1
    let date = dayjs(time).get('date')
    return `${year}年${month}月${date}日`
}
/** 获取一个包含1-100的数组 */
export const getOneHundred = () => {
    let days = []
    for (let index = 0; index < 100; index++) {
        days[index] = { id: index, name: `${index + 1}` }
    }
    return days
}
/** 毫秒转为分 */
export const getGapTime = (second: any) => {
    if (second != 0) {
        second = second / 1000
    }
    //转换天数
    let days = second / 86400
    //剩余秒数
    second = second % 86400
    //转换小时数
    let hours = second / 3600
    //剩余秒数
    second = second % 3600
    //转换分钟
    let minutes = second / 60
    //剩余秒数
    second = second % 60
    let s = days + '天' + hours + '时' + minutes + '分' + second + '秒'
    if (0 < days) {
        return { day: days, hour: hours, minute: minutes, second: second }
    } else {
        return { hour: hours, minute: minutes, second: second } //hours + "时" + minutes + "分" + second + "秒"
    }
}

/* 跨年显示年方法 */
export const getDateNewYear = (time: any) => {
    let getYear = joinZreo(dayjs().get('year'))
    let year = joinZreo(dayjs(time).get('year'))
    if (getYear < year) {
        console.log(`${year}年 ${getDateFormat(time)}`)
        return `${year} ${getDateFormat(time)}`
    } else {
        return getDateFormat(time)
    }
}
/***
 * 时间戳格式化
 * @param time 时间戳
 * @param fmt
 * @returns {string}
 */
export const formatTime = (time: any, fmt: string = 'yyyy-MM-dd hh:mm:ss'): string => {
    if (time === 0 || !time) {
        return '';
    }
    if (time.toString().length < 13) {
        time = time * 1000;
    }
    let date = new Date(time);
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    let dayArr: string[] = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    let o: { [key: string]: number | string } = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
        'week': dayArr[date.getDay()],
    };
    for (let k in o) {
        if (new RegExp(`(${k})`).test(fmt)) {
            let str = o[k] + '';
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : ('00' + str).substr(str.length));
        }
    }
    return fmt;
};
// 秒数转换时分秒
export const msToTime = (duration: number): string => {
    let seconds = Math.floor((duration) % 60);
    let minutes = Math.floor((duration / 60) % 60);
    let hours = Math.floor(duration / (60 * 60));

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};
// 分钟转换小时
export function convertMinutesToHHMM(totalMinutes:any) {
    let hours = Math.floor(totalMinutes / 60);
    let minutes:any = totalMinutes % 60;
    // 保证分钟数始终为两位数，如只有1位数则前面补0
    if(hours<=0) return minutes + "分钟"
    return hours + "小时" + minutes + "分钟";
}

export function isCurYear (date: string): string {
	const dateToCheck = dayjs(date)
	const currentYear = dayjs().year()
	const isThisYear = dateToCheck.year() === currentYear
	const checkTime = date.split('-')
	if (isThisYear) {
		return `${checkTime[1]}月${checkTime[2]}日`
	}else {
		return `${checkTime[0]}年${checkTime[1]}月${checkTime[2]}日`
	}
}

export function getDateWeek(time: string) {
    const weekDay = dayjs(time).day()
    let week
    switch (weekDay) {
      case 0:
        week = '周日'
        break
      case 1:
        week = '周一'
        break
      case 2:
        week = '周二'
        break
      case 3:
        week = '周三'
        break
      case 4:
        week = '周四'
        break
      case 5:
        week = '周五'
        break
      case 6:
        week = '周六'
        break
    }
    return week
}
export function diffNights (start: string, end: string): number {
    if (start == end) return 1
    const date1 = dayjs(start)
    const date2 = dayjs(end)
    return date2.diff(date1, 'day') || 0
}

export function dateScope(start: string, end: string, date: string): boolean {
    const startDate = dayjs(start);
    const endDate = dayjs(end);
    const dateToCheck = dayjs(date);
    if (startDate.isSame(dateToCheck) || endDate.isSame(dateToCheck)) return true
    return dateToCheck.isAfter(startDate) && dateToCheck.isBefore(endDate)
  }