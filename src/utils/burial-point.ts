import { getSystemVersion } from "./is"
import { getHbcUserInfo, Ready } from "./mpaas/alipayUtil"
import { StorageKey } from "./storage"
import { StorageSingleton } from "./storage-util"

/**
 * 调用原生方法 上报友盟埋点
 * @param eventName
 * @param params
 */
export function burialPointByApp(eventName: string, params: Record<string, string> = {}) {
  // #ifdef H5
  // const eventData = generatePointData(eventName, params)
  if (import.meta.env.MODE !== "production") {
    console.log("开始埋点", eventName, params)
    return
  }
  generatePointData(eventName, params).then((eventData) => {
    Ready(() => {
      AlipayJSBridge.call("um_event", eventData, (result) => {
        console.log("上报埋点成功", eventData)
      })
    })
  })
  // #endif

  // #ifdef MP-WEIXIN
  // 在任何组件或JS文件中使用
  // @ts-ignore
  getApp<any>().globalData?.$uMEvent(eventName, params)
  // #endif
}

async function generatePointData(eventName: string, params: Record<string, string>) {
  const oseInfo = getSystemVersion()
  const user: any = await getHbcUserInfo()
  const userInfo = JSON.parse(user.userInfo)
  const eventData = {
    event: eventName,
    data: {
      ...params,
      version: AlipayJSBridge?.startupParams?.CClxAppVersion,
      mpaas_version: AlipayJSBridge?.startupParams?.CClxAppVersion,
      // #ifdef MP-WEIXIN
      flavor: "c_wxmn",
      // #endif
      // #ifdef H5
      flavor: "c_app",
      // #endif

      os: oseInfo.os,
      osVersion: oseInfo.version,
      userId: userInfo?.userId,
    },
  }
  return eventData
}
