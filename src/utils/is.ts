import { getHbcUserInfo } from "@/utils/mpaas/alipayUtil"

const { toString } = Object.prototype

export function is(val: unknown, type: string) {
  return toString.call(val) === `[object ${type}]`
}

export function isDef<T = unknown>(val?: T): val is T {
  return typeof val !== "undefined"
}

export function isUnDef<T = unknown>(val?: T): val is T {
  return !isDef(val)
}

export function isObject(val: any): val is Record<any, any> {
  return val !== null && is(val, "Object")
}

export function isEmpty<T = unknown>(val: T): val is T {
  // eslint-disable-next-line @typescript-eslint/no-use-before-define
  if (!(isArray(val) || isString(val))) {
    if (val instanceof Map || val instanceof Set) {
      return val.size === 0
    }
    if (isObject(val)) {
      return Object.keys(val).length === 0
    }
    return false
  }
  return val.length === 0
}

export function isDate(val: unknown): val is Date {
  return is(val, "Date")
}

export function isNull(val: unknown): val is null {
  return val === null
}

export function isNullAndUnDef(val: unknown): val is null | undefined {
  return isUnDef(val) && isNull(val)
}

export function isNullOrUnDef(val: unknown): val is null | undefined {
  return isUnDef(val) || isNull(val)
}

export function isNumber(val: unknown): val is number {
  return is(val, "Number")
}

export function isPromise<T = any>(val: any): val is Promise<T> {
  // update-begin--author:sunjianlei---date:20211022---for: 不能既是 Promise 又是 Object --------
  // eslint-disable-next-line @typescript-eslint/no-use-before-define
  return is(val, "Promise") && isFunction(val.then) && isFunction(val.catch)
  // update-end--author:sunjianlei---date:20211022---for: 不能既是 Promise 又是 Object --------
}

export function isString(val: unknown): val is string {
  return is(val, "String")
}

export function isJsonObjectString(val: string): val is string {
  if (!val) {
    return false
  }
  return val.startsWith("{") && val.endsWith("}")
}

// eslint-disable-next-line @typescript-eslint/ban-types
export function isFunction(val: unknown): val is Function {
  return typeof val === "function"
}

export function isBoolean(val: unknown): val is boolean {
  return is(val, "Boolean")
}

export function isRegExp(val: unknown): val is RegExp {
  return is(val, "RegExp")
}

export function isArray(val: any): val is Array<any> {
  return val && Array.isArray(val)
}

export function isWindow(val: any): val is Window {
  return typeof window !== "undefined" && is(val, "Window")
}

export function isElement(val: unknown): val is Element {
  return isObject(val) && !!val.tagName
}

export function isMap(val: unknown): val is Map<any, any> {
  return is(val, "Map")
}

export const isServer = typeof window === "undefined"

export const isClient = !isServer

export function isUrl(path: string): boolean {
  const reg =
    /(((^https?:(?:\/\/)?)(?:[-;:&=\\+\\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\\+\\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\\+~%\\/.\w-_]*)?\??(?:[-\\+=&;%@.\w_]*)#?(?:[\w]*))?)$/
  return reg.test(path)
}

/**
 * 是否在微信环境
 * @returns {boolean}
 */
export function isWeChat() {
  const ua = navigator.userAgent.toLowerCase()
  const weixin = ua.indexOf("micromessenger") != -1
  return weixin
}

/**
 /**
 * 是否在mpaas环境中
 * @returns {boolean}
 */
export function isAliPay() {
  // #ifdef H5
  return (
    navigator.userAgent.indexOf("AlipayClient") > -1 ||
    navigator.userAgent.indexOf("mPaaSClient") > -1
  )
  // #endif

  // #ifdef MP-WEIXIN
  return false
  // #endif
}

export function isAndroid() {
  return uni.getSystemInfoSync().platform === "android"
}

/**
 * 获取系统版本信息
 */
export function getSystemVersion() {
  const systemInfo = uni.getSystemInfoSync()
  return {
    os: systemInfo.osName,
    version: systemInfo.osVersion,
  }
}

/**
 * 判断是否登录
 */
export async function isLogin(): Promise<boolean> {
  let loginStatus = false
  // #ifdef MP-WEIXIN
  if (uni.getStorageSync("USER_TOKEN")) {
    loginStatus = true
  } else {
    loginStatus = false
  }
  // #endif

  // #ifndef MP-WEIXIN
  const userInfo = await getHbcUserInfo()
  if (
    userInfo
      ? userInfo?.userToken
        ? userInfo?.userToken || ""
        : JSON.parse(userInfo?.userInfo)?.userToken || ""
      : ""
  ) {
    loginStatus = true
  }
  // #endif
  return loginStatus
}
