import { useUserStore } from "@/store"

const buildRequestHeaders = (headers: { key: string; value: string }[]): Record<string, string> => {
  return headers.reduce((acc, curr) => ({ ...acc, [curr.key]: curr.value }), {})
}
/**
 * 设置header
 */
export const setAuthHeaders = () => {
  const userStore = useUserStore()
  const { token, userDeviceIdentify } = userStore.userInfo as unknown as IUserInfo
  // 登录成功才会设置header sign
  if (!token) {
    return {}
  }
  const configHeaders = []
  return buildRequestHeaders(configHeaders)
}
