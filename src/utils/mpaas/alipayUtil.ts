import { isAliPay, isAndroid } from "@/utils/is"
import { APP_LOGIN } from "@/utils/mpaas/nativeKey"
import { UserInfo } from "@/types/types"
import { StorageKey } from "../storage"
import { StorageSingleton } from "../storage-util"
import { getHbcStorage } from "./miniToNative"

export function Ready(callback: any) {
  if (isAliPay()) {
    // 如果 jsbridge 已经注入则直接调用
    // @ts-ignore
    if (window.AlipayJSBridge) {
      callback && callback()
    } else {
      // 如果没有注入则监听注入的事件
      // @ts-ignore
      document.addEventListener("AlipayJSBridgeReady", callback, false)
    }
  }
}

const generateOptions = (url: string) => {
  return {
    showProgress: "NO",
    showTitleBar: "NO",
    transparentTitle: "always",
  }
}

/**
 * 打开一个链接页
 * @param url /view/main/index.html
 * @param param 参数对象
 */
export function pushWindow(url: string, params: any = {}) {
  if (import.meta.env.MODE == "locality") {
    window.location.href = !url.startsWith("http") ? location.origin + url : url
  } else {
    console.log("pushWindow", url, params)

    // #ifdef H5
    if (url.includes(".pdf") && url.startsWith("http")) {
      openWxMiniPdf(url)
      return
    }

    AlipayJSBridge.call("pushWindow", {
      url,
      param: {
        ...generateOptions(url),
        ...params,
      },
      passData: {
        ...generateOptions(url),
        ...params,
      },
    })

    // #endif

    // #ifdef MP-WEIXIN

    const isWebView = url.startsWith("http")

    if (isWebView) {
      if (url.includes(".pdf")) {
        uni.navigateTo({
          url: "/pagesOrder/pdf/pdf?url=" + url,
        })
      } else {
        uni.navigateTo({
          url: "/pages/chat/index?url=" + encodeURIComponent(url),
        })
      }
    } else {
      uni.navigateTo({
        url,
      })
    }

    // #endif
  }
}

/**
 * 关闭当前页
 */
export function popWindow(backData: any = {}) {
  if (isAliPay()) {
    AlipayJSBridge.call("popWindow", {
      data: backData,
    })
    // exitApp()
  } else {
    // #ifdef MP-WEIXIN

    console.log("getCurrentPages().length,点击返回", getCurrentPages().length)
    // 这里判断是否只有这一个页面
    const length = getCurrentPages().length === 1
    if (length) {
      uni.reLaunch({
        url: "/pages/home/<USER>",
      })
    } else {
      console.log("getCurrentPages().length,点正常发怒会击返回")

      uni.navigateBack()
    }

    // #endif
  }
}

/**
 * 打开其他应用
 * @param appId
 * @param params {
 *    url:/view/main/index.html
 * }
 * @param callback
 */
export function startApp(
  appId: string,
  params: {
    url?: string
    [key: string]: any
  } = {
    appClearTop: false,
    startMultApp: "YES",
  },
  callback: any = (res: any) => {},
) {
  if (import.meta.env.MODE == "locality") {
    // alert(params.url)
    // alert(location.origin + params.url)
    AlipayJSBridge.call(
      "startApp",
      {
        appId,
        param: {
          ...params,
          url: !params.url?.startsWith("http") ? location.origin + params.url : params.url,
          ...generateOptions(params.url || ""),
        },
      },
      function (result) {
        callback && callback(result)
      },
    )
  } else {
    // #ifdef H5
    AlipayJSBridge.call(
      "startApp",
      {
        appId,
        param: {
          ...params,
          ...generateOptions(params.url || ""),
          appClearTop: false,
          startMultApp: "YES",
        },
      },
      function (result) {
        callback && callback(result)
      },
    )
    // #endif

    // #ifdef MP-WEIXIN
    const formattedUrl = params.url.replace("/index.html#", "")
    uni.navigateTo({
      url: formattedUrl,
    })
    // #endif
  }
}

/**
 * 退出当前应用
 */
export function exitApp() {
  if (isAndroid()) {
    AlipayJSBridge.call("exitApp", {
      animated: "false",
    })
  } else {
    AlipayJSBridge.call("exitApp", {
      closeActionType: "exitTop",
      animated: "false",
    })
  }
}

/**
 * 发送通知
 * @param key
 * @param params
 */
export function postNotification(key: string, params: object) {
  Ready(() => {
    AlipayJSBridge.call(
      "postNotification",
      {
        name: key,
        data: params,
      },
      function (result) {},
    )
  })
}

/**
 * 接收通知
 * @param key
 * @param cb
 */

export function addNotifyListenerH5(key: string, cb: any) {
  Ready(() => {
    removeNotifyListenerH5(key)
    AlipayJSBridge.call(
      "addNotifyListener",
      {
        name: `NEBULANOTIFY_${key}`,
      },
      cb,
    )
  })
}

export function removeNotifyListenerH5(key: string) {
  Ready(() => {
    AlipayJSBridge.call("removeNotifyListener", {
      name: `NEBULANOTIFY_${key}`,
    })
  })
}

const emptyJson = JSON.stringify({})

export function getAppData() {}

export function getAPDataStoragePromise(key: any) {
  // #ifdef H5
  if (!isAliPay()) {
    return Promise.resolve({
      data: {},
    })
  }
  return new Promise((resolve, reject) => {
    Ready(() => {
      AlipayJSBridge.call(
        "getAPDataStorage",
        {
          type: "common",
          business: "HBC_DATA",
          key,
        },
        function (result) {
          if (result?.success) {
            resolve(result)
          } else {
            resolve({
              data: null,
            })
          }
        },
      )
    })
  })
  // #endif

  // #ifdef MP-WEIXIN
  return Promise.resolve({
    data: uni.getStorageSync(StorageKey.ACCESS_TOKEN) || null,
  })
  // #endif
}

function tryParseJSON(str: string) {
  try {
    // 尝试解析字符串
    const json = JSON.parse(str)
    // 如果解析成功，返回解析后的对象
    return json
  } catch (e) {
    // 如果解析失败，返回原始字符串
    return str
  }
}

/**
 * 添加返回类型
 */
export const getHbcUserInfo = async (): Promise<UserInfo | null> => {
  // #ifdef H5
  if (!isAliPay()) {
    return Promise.resolve({
      userInfo: JSON.stringify({
        userId: "***********",
        userInfo: {
          areaCode: "86",
          avatar: "https://fr-static-new.oss-cn-hangzhou.aliyuncs.com/capp/default_avatar.png",
          gender: 0,
          mobile: "17316124560",
          mpOpenId: "",
          nickName: "用户173****4560",
          openId: "",
          registerChannelName: "",
          registerSource: 1,
          registerSourceName: "CAPP",
          unionId: "",
          userId: "***********",
          userType: 1,
        },
        userToken: "64fb490d08ab7f5912db137fc7c1eb8b",
      }),
    } as any)
  }
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    getAPDataStoragePromise("HBC_USER_INFO").then((data: any) => {
      if (data.data && data?.data?.includes("userToken")) {
        const userInfo: UserInfo = tryParseJSON(data.data)
        resolve(userInfo)
      } else {
        resolve(null)
      }
    })
  })
  // #endif

  // #ifdef MP-WEIXIN
  const userObj = uni.getStorageSync(StorageKey.USER_INFO)

  const isDevTools = wx.getSystemInfoSync()
  let relaUser = {}
  if (isDevTools.platform.includes("devtools")) {
    relaUser = {
      userId: "***********",
      userInfo: {
        areaCode: "86",
        avatar: "https://fr-static-new.oss-cn-hangzhou.aliyuncs.com/capp/default_avatar.png",
        gender: 0,
        mobile: "17316124560",
        mpOpenId: "",
        nickName: "用户173****4560",
        openId: "",
        registerChannelName: "",
        registerSource: 1,
        registerSourceName: "CAPP",
        unionId: "",
        userId: "***********",
        userType: 1,
      },
      userToken: "64fb490d08ab7f5912db137fc7c1eb8b",
    }
    return {
      userInfo: JSON.stringify(relaUser),
      userId: relaUser.userId,
    } as any
  }

  if (typeof userObj === "string" && userObj?.length > 2) {
    relaUser = JSON.parse(userObj)
  } else {
    relaUser = userObj
  }
  return {
    userInfo: JSON.stringify(relaUser),
    userId: relaUser.userId,
  } as any
  // #endif
}

/**
 * 去登录
 */
export function goLogin() {
  // #ifdef H5
  AlipayJSBridge.call(APP_LOGIN, {}, (result) => {
    if (result.result == "-1") {
      AlipayJSBridge.call(
        APP_LOGIN,
        {
          action: "-1",
        },
        () => {
          AlipayJSBridge.call(APP_LOGIN)
        },
      )
    }
  })
  AlipayJSBridge.call(
    "removeAPDataStorage",
    {
      type: "common",
      business: "HBC_DATA",
      key: "HBC_USER_INFO",
    },
    function (result) {
      window.location.reload()
    },
  )
  // #endif

  // #ifdef MP-WEIXIN
  uni.navigateTo({
    url: "/subPackagesB/pages/login/loginRegister?fromPage=back",
  })
  // #endif
}

export async function openMiniApp(params: { appId: string; page: string }) {
  // #ifdef H5
  const userInfo = await getHbcUserInfo()
  const HBC_AK: any = await getAPDataStoragePromise("HBC_AK")
  const ak = HBC_AK.data
  const data = {
    page: params.page,
    query: `CClxAppVersion=${AlipayJSBridge.startupParams.CClxAppVersion}&userInfo=${
      userInfo?.userInfo
    }&appClearTop=${false}&startMultApp=YES&ak=${ak}`,
  }
  console.log(`-------------------  userInfo?.userInfo:${userInfo?.userInfo}   -------------------`)

  AlipayJSBridge.call(
    "startApp",
    {
      // 要打开页面的 URL
      appId: params.appId,
      // 打开页面的配置参数
      param: data,
      appClearTop: false,
      startMultApp: "YES",
    },
    (result) => {
      alert(JSON.stringify(result))
    },
  )
  // #endif

  // #ifdef MP-WEIXIN

  uni.navigateTo({
    url: params.page,
  })
  // #endif
}

export const imageViewer = (imageUrls: string[], position = 0) => {
  Ready(() => {
    AlipayJSBridge.call(
      "imageViewer",
      {
        images: imageUrls.map((res) => {
          return {
            u: res,
          }
        }),
        init: position,
        enablesavephoto: true,
        enableShowPhotoDownload: true,
      },
      function (result) {
        console.log(result)
      },
    )
  })
}

export const openWxMiniPdf = (url: string) => {
  const params = {
    appid: "gh_5fb95a9f65e5",
    path: `/subPackageTravel/pagesOrder/pdf/pdf?url=${encodeURIComponent(url)}`,
    debug: 0,
    query: {
      fromPage: "",
      url: url,
    },
  }
  AlipayJSBridge.call("pay_mini_go", params)
}
