export const HideTitleBar = "hide_title_bar"
export const HIDE_NATIVE_BACK = "hide_native_back"
export const SHOW_NATIVE_BACK = "show_native_back"
export const ShowTitleBar = "show_title_bar"
export const CloseMiniApp = "close_mini_app"
export const OpenMapNavigation = "open_map_navigation"
export const OpenWebView = "open_web_view"
export const FinishMain = "finish_main"
export const ReceivePush = "receive_push"
export const GetLocation = "get_location"
export const RequestPermission = "request_permission"
export const GetRegisterId = "get_register_id"
export const ClickPush = "click_push"
export const LoginInfoEvent = "login_info_event"
export const PayMiniGo = "pay_mini_go"
export const SHARE_WX = "share_wx"
export const PayAlipayGo = "pay_alipay_go"
export const LoginWX = "login_wx"
export const InstallWX = "install_wx"
export const START_DEBUG = "start_debug"
export const LoginApple = "login_apple"
export const LoginOneClickVerification = "login_verification_enable" // 校验是否可以一键登录
export const LoginOneClick = "login_verification" // 一键登录
export const CheckNotification = "check_notification"
export const SettingNotification = "setting_notification"
export const CLOSE_NATIVE_MAIN = "close_native_main"
export const GET_APP_CHANNEL = "get_app_channel" // 获取app渠道号
export const ON_NATIVE_MAIN = "on_native_main"

export const GET_APP_VERSION = "get_app_version"
export const APP_LOGIN = "app_login" // 跳转app登录
export const OPEN_MINI_APP = "open_mini_app" // 打开小程序
export const SWITCH_TAB = "switch_tab" // 切到首页tab
export const SEND_H5_DATA = "sendH5Data" // 发送数据到离线包
export const BACK_TO_HOME = "back_to_home" // 回到首页
export const GET_HBC_STORAGE = "get_hbc_storage" // 获取mpaas壳上的数据
export const SET_HBC_STORAGE = "set_hbc_storage" // 向mpaas壳上存储数据
export const FORCE_UPDATE_MINI_ALL = "force_update_mini_all" // 主动更新小程序
export const FORCE_RESTART_MINI_APP = "force_restart_mini_app" // 主动重新加载小程序
export const SET_BADGE_COUNT = "set_badge_count" // 设置角标
