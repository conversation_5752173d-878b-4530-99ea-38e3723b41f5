import { isAli<PERSON><PERSON>, isAndroid, isWeChat } from "../is"
import platform from "../platform"
import { StorageSingleton } from "../storage-util"
import { getHbcUserInfo, Ready } from "./alipayUtil"
import {
  APP_LOGIN,
  CheckNotification,
  CloseMiniApp,
  FinishMain,
  GET_HBC_STORAGE,
  GetLocation,
  HIDE_NATIVE_BACK,
  HideTitleBar,
  LoginOneClickVerification,
  OpenMapNavigation,
  OpenWebView,
  PayAlipayGo,
  RequestPermission,
  SET_HBC_STORAGE,
  SettingNotification,
  ShowTitleBar,
} from "./nativeKey"

export function hideNavigationBar() {
  Ready(() => {
    AlipayJSBridge.call(HideTitleBar, {}, (result) => {})
    AlipayJSBridge.call("hideTitlebar")
  })
}

export function showNavigationBar() {
  if (isWeChat()) return
  Ready(() => {
    AlipayJSBridge.call(ShowTitleBar, {}, (result) => {})
    AlipayJSBridge.call("showTitlebar", {}, (result) => {})
  })
}

// export function goLogin() {
//   if (isWeChat()) return
//   // startApp('10000004', {
//   //   showTitleBar: 'NO',
//   //   showProgress: 'NO',
//   //   backBehavior: 'back',
//   // })
//   AlipayJSBridge.call(APP_LOGIN, {}, (result) => {
//     if (result.result == "-1") {
//       AlipayJSBridge.call(
//         APP_LOGIN,
//         {
//           action: "-1",
//         },
//         () => {
//           AlipayJSBridge.call(APP_LOGIN)
//         },
//       )
//     }
//   })
// }
/**
 * 关闭手势
 */
export function closeGestureBack() {
  if (isWeChat()) return
  AlipayJSBridge.call("setGestureBack", {
    val: true,
  })
}

/**
 * 打开手势
 */
export function openGestureBack() {
  if (isWeChat()) return
  AlipayJSBridge.call("setGestureBack", {
    val: true,
  })
}

export function hideNavBack() {
  AlipayJSBridge.call(HIDE_NATIVE_BACK, {}, (result) => {})
}

export function closeMiniApp() {
  AlipayJSBridge.call(CloseMiniApp, {}, (result) => {})
}

export function openMapNavigation() {
  AlipayJSBridge.call(OpenMapNavigation, {}, (result) => {})
}

export function openWebView(params: any) {
  AlipayJSBridge.call(OpenWebView, params, (result) => {})
}

export function finishMain() {
  AlipayJSBridge.call(FinishMain, {}, (result) => {})
}

export function CloseNativeMain() {}

export function getLocationNative() {
  return new Promise((resolve, reject) => {
    AlipayJSBridge.call(GetLocation, {}, (result) => {
      if (result.code === "1") {
        resolve(result.data)
      } else {
        reject()
      }
    })
  })
}

/** 读取定位权限 */
export function requestLocationPermission() {
  if (isAndroid()) {
    AlipayJSBridge.call(RequestPermission, { list: ["location"] }, (result) => {
      // console.log('result-----------------', result)
      if (result.code === "1") {
        // 有权限
      } else {
      }
    })
  }
}

/** 读取相机权限 */
export function requestCamera() {
  if (isAndroid()) {
    AlipayJSBridge.call(
      RequestPermission,
      { list: ["camera", "storage", "mediaImages", "mediaVideo"] },
      (result) => {
        // console.log('result-----------------', result)
        if (result.code === "1") {
          // 有权限
        } else {
        }
      },
    )
  }
}

/** APP调起微信小程序去支付 */
export function appWXPayGo(queryParmes: any) {}

/** APP支付宝支付 */
export function appAlipayPayGo(parmes: any) {
  return new Promise((resolve, reject) => {
    AlipayJSBridge.call(PayAlipayGo, parmes, (result) => {
      if (result.code === "1") {
        resolve(result.message)
      } else {
        reject(result)
      }
    })
  })
}

/** 校验是否可以一键登录 */
export function loginOneClickVerification() {
  return new Promise((resolve, reject) => {
    AlipayJSBridge.call(LoginOneClickVerification, {}, (result) => {
      if (result.code === "1") {
        resolve(result)
      } else if (result.code === "-1") {
        resolve(result)
      } else {
        reject()
      }
    })
  })
}

/** 校验推送权限 */
export function checkNotification() {
  return new Promise((resolve, reject) => {
    AlipayJSBridge.call(CheckNotification, {}, (result) => {
      if (result) {
        resolve(result)
      } else {
        // eslint-disable-next-line prefer-promise-reject-errors
        reject()
      }
    })
  })
}

/** 调用系统设置开启推送权限 */
export function settingNotification() {
  return new Promise((resolve, reject) => {
    AlipayJSBridge.call(SettingNotification, {}, (result) => {
      if (result.code === "1") {
        resolve(result)
      } else if (result.code === "-1") {
        resolve(result)
      } else {
        reject()
      }
    })
  })
}

export function getHbcStorage(key: any) {
  // #ifdef H5
  return new Promise((resolve, reject) => {
    AlipayJSBridge.call(GET_HBC_STORAGE, { key }, (result) => {
      resolve(result.result)
    })
  })
  // #endif

  // #ifdef MP-WEIXIN
  return uni.getStorageSync(key) || null
  // #endif
}

export function setHbcStoragePromise(key: any, value: any) {
  if (isAliPay()) {
    return new Promise((resolve, reject) => {
      AlipayJSBridge.call(SET_HBC_STORAGE, { key, value }, (result) => {
        if (result.result == 1) {
          resolve(true)
        } else {
          reject(false)
        }
      })
    })
  } else {
    StorageSingleton.getInstance().setItem(key, value)
  }
  return Promise.resolve(true)
}

export function setHbcStorage(key: any, value: any) {
  AlipayJSBridge.call(SET_HBC_STORAGE, { key, value }, (result) => {})
}

export function openMiniApp(params: { appId: string; page: string }) {
  // console.log(`openMiniApp:${typeof userInfo}`)
  const data = {
    page: params.page,
    query: `CClxAppVersion=${AlipayJSBridge.startupParams.CClxAppVersion}&appClearTop=${false}&startMultApp=YES`,
  }
  AlipayJSBridge.call(
    "startApp",
    {
      // 要打开页面的 URL
      appId: params.appId,
      // 打开页面的配置参数
      param: data,
    },
    (result) => {
      // alert(JSON.stringify(result))
    },
  )
}

export function getMiniAppForceResult() {}

export const openNativeIm = (groupId: any) => {
  AlipayJSBridge.call("cclx_im_open_chat", {
    isGroup: true,
    chatID: groupId,
  })
}

export const openV2MiniApp = (data: {}) => {
  AlipayJSBridge.call("open_mini_app", data, (value: any) => {})
}

export function openCappMiniApp(params: { appId: string; page: string }) {
  // #ifdef H5
  getHbcUserInfo().then((result) => {
    const userInfo = result ? result.userInfo : JSON.stringify({})
    AlipayJSBridge.call("startApp", {
      // 要打开页面的 URL
      appId: params.appId,
      // 打开页面的配置参数
      param: {
        page: params.page,
        query: `CClxAppVersion=${AlipayJSBridge.startupParams.CClxAppVersion}&userInfo=${userInfo}&showTitleBar=YES&appClearTop=${false}&startMultApp=YES`,
        appClearTop: false,
        startMultApp: "YES",
      },
      appClearTop: false,
      startMultApp: "YES",
    })
  })
  // #endif

  // #ifdef MP-WEIXIN

  uni.navigateTo({
    url: params.page,
  })
  // #endif
}

export const callIm = (groupId: any) => {
  AlipayJSBridge.call("cclx_call_launch", {
    isGroup: true,
    groupId,
  })
}
