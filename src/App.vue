<template>
  <nut-config-provider theme="dark">
    <nut-navbar class="navBar"></nut-navbar>
  </nut-config-provider>
</template>

<script setup lang="ts">
onLaunch(() => {})

onShow(() => {
  console.log("App Sh1ow")
})
onHide(() => {
  console.log("App Hide")
})
</script>

<style lang="scss">
@import "nutui-uniapp/styles/index.scss";

page {
  background-color: #f5f5f5;
  font-family:
    -apple-system, BlinkMacSystemFont, "PingFang SC", "Helvetica Neue", Helvetica,
    "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
}
</style>
