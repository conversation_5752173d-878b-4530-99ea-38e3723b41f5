<template>
  <view class="w-full">
    <!-- #ifdef H5 -->
    <nut-navbar
      :title="title"
      :style="{
        background: bgColor,
        fontWeight: '500',
      }"
      fixed
      :custom-color="titleColor"
      :safe-area-inset-top="true"
      :left-show="showBack"
    >
      <template #leftShow>
        <!-- <nut-icon name="rect-left" custom-color="#333" size="22" @click="onBack"></nut-icon> -->
        <image
          :src="'../../static/icon/back.svg'"
          class="w-[48rpx] h-[48rpx]"
          @click="onBack"
        ></image>
      </template>
    </nut-navbar>
    <!-- #endif -->

    <slot></slot>
  </view>
</template>

<script setup lang="ts">
import { popWindow } from "@/utils/mpaas/alipayUtil"

defineProps({
  title: {
    type: String,
    default: "",
  },
  bgColor: {
    type: String,
    default: "#fff",
  },
  titleColor: {
    type: String,
    default: "rgba(0, 0, 0, 0.90)",
  },
  showBack: {
    type: Boolean,
    default: true,
  },
})
const onBack = () => {
  // alert("asd")
  popWindow()
}
</script>
<script lang="ts">
// #ifdef MP-WEIXIN

export default {
  options: {
    styleIsolation: "shared",
  },
}
// #endif
</script>

<style lang="scss" scoped>
:deep(.nut-navbar--fixed) {
  box-shadow: none;
}

:deep(.nut-navbar__left) {
  padding: 0 20rpx;
}

:deep(.nut-navbar) {
  box-shadow: none;
}

.min-h-full {
  --nut-navbar-box-shadow: none;
  --nut-navbar-title-font-weight: bold;
}
</style>
