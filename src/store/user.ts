import { defineStore } from "pinia"
import { ref } from "vue"
const initState = {}
export const useUserStore = defineStore(
  "user",
  () => {
    // im 令牌
    const imAccountInfo = ref<{
      imToken: string
      imId: string
    }>({
      imToken:
        "eJw1jk0LgkAURf-LrEPmPcevoEUoImRi5KY2IjrJy5JJzYLovzeJLu*598D9sCw*GvKtqJNszSxwBeecrSY8yk4zNJbcV02hFFUagq1nQtjcnDuqZDvQhSYFEIVwHbBQ5JDjolOtu8Y-9c9bF6u*3mXBHiHcHtr0UaskKTHyy*g1XtOgDL2zu5nFge7-c*AIz*G2Cci*P2kPM4g_",
      imId: "12244871524_1_2",
    })
    const updateImAccountInfo = (info: { imToken: string; imId: string }) => {
      imAccountInfo.value = info
    }
    return {
      imAccountInfo,
      updateImAccountInfo,
    }
  },
  {
    persist: true,
  },
)
