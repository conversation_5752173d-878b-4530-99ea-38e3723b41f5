{"easycom": {"autoscan": true, "custom": {"^nut-([^-]+)-(.+)$": "nutui-uniapp/components/$1$2/$1$2.vue", "^nut-(.*)": "nutui-uniapp/components/$1/$1.vue"}, "debug": true}, "globalStyle": {"backgroundColor": "#F8F8F8", "navigationBarBackgroundColor": "#f8f8f8", "navigationBarTextStyle": "black", "navigationStyle": "custom", "navigationBarTitleText": ""}, "pages": [{"path": "pages/home-didi/index", "style": {"navigationBarTitleText": "滴滴首页", "enablePullDownRefresh": true}}]}