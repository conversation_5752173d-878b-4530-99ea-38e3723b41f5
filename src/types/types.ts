export interface UserInfo {
  userId: string
  userInfo: {
    areaCode: string
    avatar: string
    gender: number
    mobile: string
    mpOpenId: string
    nickName: string
    openId: string
    registerChannelName: string
    registerSource: number
    registerSourceName: string
    unionId: string
    userId: string
    userType: number
  }
}

/**
 * 发起包车请求
 */
export interface FrontInitTravel {
  cityId: string
  cityName: string
  imgUrl?: string
  location: string
  startDate: string
  startTime: string
  type: string
}

export interface TravelOrderSearchParams {
  carModelId?: string // 车型编号
  cityId?: string // 出发城市编号
  cityName?: string // 出发城市编号
  daySize?: number // 游玩天数
  endDate?: string // 结束日期，格式：yyyy-MM-dd
  maxPrice?: number // 最高价格，单位：元
  minPrice?: number // 最小价格，单位：元
  pageNum?: number // 页数，默认值为 1
  pageSize?: number // 每页大小，默认值为 10
  sortedBy?: string // 价格排序字段，默认值为 "highPrice"，可选值为 "lowPrice" 或 "highPrice"
  startDate?: string // 出发日期，格式：yyyy-MM-dd
  tagIds?: string[] // 标签编号列表
  travelLineId?: string // 线路编号
  tagId1?: string[]
  tagId2?: string[]
  tagId3?: string[]
  tagId4?: string[]
  tagId5?: string[]
}
