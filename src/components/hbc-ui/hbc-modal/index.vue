<template>
  <nut-popup
    :close-on-click-overlay="maskClick"
    :custom-style="{ width: '600rpx', maxHeight: '496rpx', borderRadius: '8rpx', padding: '32rpx' }"
    v-model:visible="visible"
  >
    <view class="flex-row flex-center relative">
      <text class="font-500 text-#262626 text-32rpx h-40rpx">{{ title }}</text>
      <view v-if="showClose" class="absolute right-0 flex-row flex-center" @click="onCancel">
        <nut-icon name="close" szie="16" color="#595959"></nut-icon>
      </view>
    </view>
    <view class="mt-32rpx mb-40rpx max-h-252rpx text-center">
      <view class="font-300 text-#595959 leading-[37rpx] text-26rpx">{{ content }}</view>
      <slot></slot>
    </view>
    <view class="flex-row flex-center justify-between bottom-btn">
      <nut-button type="default" v-if="showCancel" class="flex-1" @click="onCancel">
        {{ cancelText }}
      </nut-button>
      <view :class="{ 'w-24rpx': showCancel }"></view>
      <nut-button type="primary" class="flex-1" @click="onOk">{{ okText }}</nut-button>
    </view>
  </nut-popup>
</template>

<script lang="ts" setup>
const props = defineProps({
  /**
   * 是否显示取消按钮
   */
  showCancel: {
    type: Boolean,
    default: true,
  },
  modelValue: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
  content: {
    type: String,
    default: "",
  },
  maskClick: {
    type: Boolean,
    default: true,
  },
  showClose: {
    type: Boolean,
    default: true,
  },
  cancelText: {
    type: String,
    default: "取消",
  },
  okText: {
    type: String,
    default: "确定",
  },
})
const emit = defineEmits(["update:modelValue", "onCancel", "onOk"])
const visible = ref(false)
watch(
  () => props.modelValue,
  (value) => {
    visible.value = value
  },
)
const onCancel = () => {
  visible.value = false
  emit("onCancel")
}
const onOk = () => {
  visible.value = false
  emit("onOk")
}
</script>

<style lang="scss" scoped>
.bottom-btn {
  --nut-button-border-radius: 8rpx;
}
</style>
