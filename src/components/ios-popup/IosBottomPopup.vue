<template>
  <nut-popup
    position="bottom"
    :custom-style="popupStyle"
    round
    :visible="modelValue"
    @update:visible="updateVisible"
    :z-index="9999"
    @open="onOpen"
    :destroy-on-close="destroyOnClose"
    @click-overlay="onBackClick"
  >
    <view
      class="ios-popup-content"
      :style="contentStyle"
      @touchstart="handleTouchStart"
      @touchmove.stop="handleTouchMove"
      @touchend="handleTouchEnd"
    >
      <!-- 拖动条 -->
      <view class="drag-handle" v-if="showIosTool"></view>

      <!-- 导航栏 -->
      <view class="nav-bar m-b-5" v-if="showIosTool">
        <view class="nav-left">
          <view v-if="showBackButton" class="back-button" @click="onBackClick">
            <nut-icon name="rect-left" custom-color="#333" size="22"></nut-icon>
          </view>
        </view>
        <view class="nav-title">{{ title }}</view>
        <view class="nav-right">
          <text v-if="showDoneButton" class="done-button" @click="onDoneClick">完成</text>
        </view>
      </view>

      <!-- 弹窗内容区域 -->
      <view class="popup-content">
        <slot></slot>
      </view>
    </view>
  </nut-popup>
</template>

<script setup lang="ts">
// 组件props
const props = defineProps({
  /**
   * 弹窗显示状态，支持 v-model 绑定
   */
  modelValue: {
    type: Boolean,
    default: false,
  },

  showIosTool: {
    type: Boolean,
    default: true,
  },
  /**
   * 弹窗标题
   */
  title: {
    type: String,
    default: "",
  },
  /**
   * 是否显示返回按钮
   */
  showBackButton: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否显示完成按钮
   */
  showDoneButton: {
    type: Boolean,
    default: true,
  },
  /**
   * 点击遮罩是否关闭弹窗
   */
  closeOnClickOverlay: {
    type: Boolean,
    default: true,
  },
  /**
   * 弹窗高度
   */
  popupHeight: {
    type: String,
    default: "calc(100vh - 200rpx)",
  },
  /**
   * 关闭时是否销毁DOM内容
   * 设为false可以保留DOM状态，提高再次打开的性能
   */
  destroyOnClose: {
    type: Boolean,
    default: false,
  },
})

// 组件事件
const emits = defineEmits(["update:modelValue", "back", "done"])

/**
 * 计算弹窗样式
 */
const popupStyle = computed(() => {
  return {
    // height: props.popupHeight,
    // borderRadius: "16rpx 16rpx 0 0",
    // overflow: "hidden",
    // position: "relative",
    // background: "red",
  }
})

// 拖动相关变量
const startY = ref(0) // 触摸开始的Y坐标
const moveY = ref(0) // 当前移动的Y坐标
const isDragging = ref(false) // 是否正在拖动
const translateY = ref(0) // Y轴位移量

/**
 * 计算内容样式，用于实现拖动效果
 */
const contentStyle = computed(() => {
  // 计算弹窗高度，随着拖动减小
  const height = `calc(90vh - ${translateY.value}px)`
  return {
    height: height,
    transition: isDragging.value ? "none" : "height 0.3s ease",
  }
})

/**
 * 处理触摸开始事件
 * @param e 触摸事件对象
 */
const handleTouchStart = (e: TouchEvent) => {
  // 整个弹窗区域都可以触发拖动
  startY.value = e.touches[0].clientY
  isDragging.value = true
  translateY.value = 0
}

/**
 * 处理触摸移动事件
 * @param e 触摸事件对象
 */
const handleTouchMove = (e: TouchEvent) => {
  if (!isDragging.value) return

  moveY.value = e.touches[0].clientY
  // 计算位移量（只允许向下拖动）
  const diff = Math.max(0, moveY.value - startY.value)
  translateY.value = diff

  // 防止拖动过大导致弹窗消失
  const maxDrag = window.innerHeight * 0.9 // 最多可以拖动原始高度的70%
  if (translateY.value > maxDrag) {
    translateY.value = maxDrag
    // 重置拖动状态
    // emits("update:modelValue", false)
  }
}

const onOpen = () => {
  isDragging.value = false
  translateY.value = 0
}
/**
 * 处理触摸结束事件
 */
const handleTouchEnd = () => {
  if (!isDragging.value) return

  // 获取屏幕高度作为参考
  // 使用uni-app的API获取系统信息，在各平台兼容
  const systemInfo = uni.getSystemInfoSync()
  const screenHeight = systemInfo.windowHeight

  // 计算弹窗原始高度（大约90vh）
  const popupHeight = screenHeight * 0.9

  // 如果拖动超过弹窗高度的50%，则关闭弹窗
  if (translateY.value > popupHeight * 0.3) {
    emits("update:modelValue", false)
  } else {
    isDragging.value = false
    translateY.value = 0
  }
}

/**
 * 点击遮罩处理
 */
const onClickOverlay = () => {
  if (props.closeOnClickOverlay) {
    emits("update:modelValue", false)
  }
}

/**
 * 更新可见性状态
 */
const updateVisible = (value: boolean) => {
  emits("update:modelValue", value)
}

/**
 * 返回按钮点击事件
 */
const onBackClick = () => {
  emits("back")
}

/**
 * 完成按钮点击事件
 */
const onDoneClick = () => {
  emits("done")
  // emits("update:modelValue", false)
}
</script>

<style scoped>
/* 弹窗内容样式 */
.ios-popup-content {
  display: flex;
  flex-direction: column;
  width: 100vw;
  /* height由JS动态控制 */
  position: relative;
  background-color: #f5f5f5;
  overflow: hidden;
}

/* 拖动条样式 */
.drag-handle {
  width: 36px;
  height: 5px;
  background-color: #e0e0e0;
  border-radius: 10px;
  margin: 8px auto 0;
  cursor: pointer;
}

/* 导航栏样式 */
.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  height: 48px;
  border-bottom: 1px solid #f5f5f5;
}

.nav-left {
  width: 80px;
}

.back-button {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  color: #000;
}

.nav-right {
  width: 80px;
  text-align: right;
}

.done-button {
  font-size: 16px;
  font-weight: bold;
  color: #000;
  cursor: pointer;
}

/* 弹窗标题样式 */
.popup-title {
  padding: 15px 20px 10px;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #000;
}

/* 弹窗内容区域样式 */
.popup-content {
  padding: 0;
  overflow-y: auto;
  max-height: calc(90vh - 80px);
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* 取消按钮区域样式 */
.cancel-area {
  padding: 10px 20px 20px;
}

.cancel-button {
  width: 100%;
  height: 48px;
  line-height: 48px;
  background-color: #f5f5f5;
  border-radius: 12px;
  color: #007aff;
  font-size: 18px;
  font-weight: 500;
  border: none;
  outline: none;
  transition: background-color 0.2s;
}

.cancel-button-hover {
  background-color: #e0e0e0;
}

/* 增强滚动体验 */
::-webkit-scrollbar {
  width: 0;
  background: transparent;
}

.no-transition {
  transition: none !important;
}

/* 防止弹窗内容滚动影响背景 */
.overflow-y-auto {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* 自定义样式补充 */
.max-h-90vh {
  max-height: 90vh;
}

/* .max-h-calc(90vh-80px) {
  max-height: calc(90vh - 80px);
} */
</style>
