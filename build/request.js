const axios = require('axios')
const fs = require('fs')
const FormData = require('form-data')
const createHttpService = (baseURL) => {
  const instance = axios.create({
    baseURL,
    timeout: 300000,
  })

  // 添加请求拦截器
  instance.interceptors.request.use(
    (config) => {
      // 在发送请求之前做些什么
      // 例如，添加请求头、处理请求参数等
      return config
    },
    (error) => {
      // 对请求错误做些什么
      return Promise.reject(error)
    },
  )

  // 添加响应拦截器
  instance.interceptors.response.use(
    (response) => {
      // 对响应数据做些什么
      return response.data
    },
    (error) => {
      // 对响应错误做些什么

      return Promise.reject(error)
    },
  )

  // 封装 GET 请求
  const get = (url, params = {}) => {
    console.log('params', params)
    return instance.get(url, { params })
  }

  // 封装 POST 请求
  const post = (url, data = {}) => {
    return instance.post(url, data)
  }

  // 封装上传文件的方法
  const uploadFile = (body) => {
    const formData = new FormData()
    Object.keys(body).forEach((key) => {
      formData.append(key, body[key])
    })
    formData.append('file', fs.createReadStream(body.file))

    return instance.post(body.host, formData, {
      headers: {
        ...formData.getHeaders(),
      },
      baseURL: body.host,
    })
  }

  return {
    get,
    post,
    uploadFile,
  }
}

module.exports = createHttpService
