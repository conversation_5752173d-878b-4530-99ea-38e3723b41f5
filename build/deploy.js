/* eslint-disable */
const OSS = require('ali-oss')
const co = require('co')
const fs = require('fs')
const path = require('path')
const chalk = require('chalk')

const client = new OSS({
  region: 'oss-cn-hangzhou',
  accessKeyId: 'LTAI5tR5qtwrDajQH5Xtafxi',
  accessKeySecret: '******************************',
  bucket: 'fr-static-new',
  timeout: 5000000,
})
class Deploy {
  /**
   * [constructor description]
   * @param {string} src  同步目录
   * @param {string} dist 阿里云目录
   */
  constructor(src, dist) {
    this.src = path.resolve(src)
    this.dist = dist
    this.count = 0
    this.fail = 0
    this.length = 0
  }
  getAllFiles(dir = path.resolve(this.src), filesList = []) {
    const files = fs.readdirSync(dir)
    const self = this
    let stat
    let obj = {}
    let abspath
    files.forEach((itm) => {
      abspath = path.resolve(dir, itm)
      stat = fs.statSync(abspath)
      if (!stat.isDirectory()) {
        obj = {} // 定义一个对象存放文件的路径和名字
        obj.path = abspath // 路径
        obj.filename = self.dist + path.resolve(dir, itm).split(self.src)[1] // 名字
        filesList.push(obj)
      } else {
        self.getAllFiles(abspath, filesList)
      }
    })
    return filesList
  }
  get allFiles() {
    return this.getAllFiles()
  }
  uploadFile(
    fileObj = {
      filename: '',
      path: '',
    }
  ) {
    const { filename, path } = fileObj
    const self = this
    co(function* () {
      const result = yield client.put(filename, path)
      if (result.res.status === 200) {
        self.count++
        console.log(chalk.green(`成功${self.count}：${result.url.replace(/http:\/\/fr-static-new\.oss-cn-hangzhou\.aliyuncs\.com/, 'https://fr-static-new.oss-cn-hangzhou.aliyuncs.com')}`))
      } else {
        self.fail++
        console.log(chalk.red(`失败${self.fail}: ${path}失败`))
      }
    })
      .then(() => {
        if (self.count === self.length) {
          console.log(chalk.green('文件已全部上传！！！'))
        }
        if (self.fail > 0) {
          console.log(chalk.red(`共有${self.fail}个文件上传失败，请重新上传！！！！`))
        }
      })
      .catch((err) => {
        console.log(chalk.red(err))
      })
  }
  uploadAllFile(arr = this.allFiles) {
    if (this.allFiles.length === 0) return
    const self = this
    this.length = arr.length
    console.log(chalk.blue(`开始部署静态资源 from  ${this.src} to ${this.dist} 共有：${arr.length}个文件 `))
    arr.forEach((item) => {
      self.uploadFile(item)
    })
  }
}

module.exports = Deploy
