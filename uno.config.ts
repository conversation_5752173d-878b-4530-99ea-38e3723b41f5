import {
  type Preset,
  defineConfig,
  presetUno,
  presetAttributify,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup,
} from "unocss"

import { presetApplet, presetRemRpx, transformerAttributify } from "unocss-applet"

// 主题配置类型定义
type ThemeConfig = {
  colors: {
    primary: {
      [key: string | number]: string
      DEFAULT: string
    }
    [key: string]: any
  }
  [key: string]: any
}

const isMp = process.env?.UNI_PLATFORM?.startsWith("mp") ?? false

// 判断是否为didi渠道
const isDidi = process.env?.UNI_CUSTOM_SCRIPT?.includes("didi")

const presets: Preset[] = []
if (isMp) {
  /**
   * 小程序端使用presetApplet
   */
  presets.push(presetApplet(), presetRemRpx())
} else {
  presets.push(presetUno(), presetAttributify())
}

// 默认主题配置
const defaultTheme: ThemeConfig = {
  colors: {
    primary: {
      50: "#FFF1F2", // 最浅，对应 primary7
      100: "#FFE0E2", // 对应 primary6
      200: "#FFC7CA", // 对应 primary5
      300: "#FFA0A5", // 对应 primary4
      400: "#FF6970", // 对应 primary3
      500: "#FF2342", // 对应 primary2（默认色）
      600: "#E71B25", // 对应 primary1
      DEFAULT: "#FF2342", // 设置默认色为 primary2
    },
  },
}

// didi主题配置
const didiTheme: ThemeConfig = {
  colors: {
    primary: {
      50: "#FFF2ED", // 最浅色调
      100: "#FFE4D9", // 淡色
      200: "#FFC9B2", // 淡色
      300: "#FFA685", // 淡色
      400: "#FF8359", // 淡色
      500: "#FF6435", // 默认色 - 滴滴橙
      600: "#E54F21", // 深色
      DEFAULT: "#FF6435", // 默认使用滴滴橙
    },
  },
}

// 根据渠道判断使用哪套主题
const currentTheme = isDidi ? didiTheme : defaultTheme
console.log("当前使用主题: ", isDidi ? "didi主题" : "默认主题")

// 直接导出配置
export default defineConfig({
  presets: [
    ...presets,
    presetIcons({
      scale: 1.2,
      warn: true,
      extraProperties: {
        display: "inline-block",
        "vertical-align": "middle",
      },
    }),
  ],
  /**
   * unocss类名缩写
   */
  shortcuts: [
    /**
     * 水平居中
     */
    ["flex-center", "flex justify-center items-center"],
    /**
     * 图片容器
     */
    ["img-container", "relative w-full h-0 pb-[100%] overflow-hidden"],
    /**
     * 标题
     */
    ["heading", "text-3xl font-bold mb-4"],
    /**
     * 卡片通用
     */
    ["card", "rounded-lg shadow-md bg-white p-4 m-2"],
    // 加个点击变成 背景变成#FAFAFA
    [
      "common-bg",
      "bg-white w-89.75   rd-1 m-t-2 p-3 text-3.5 text-color-#262626 font-700 active:bg-[#FAFAFA]",
    ],
    ["rule-text", "text-color-#595959 text-[24rpx] leading-[36rpx] font-300"],
    
  ],
  transformers: [
    transformerDirectives(),
    transformerVariantGroup(),
    transformerAttributify({
      prefixedOnly: true,
      prefix: "u",
    }),
  ],
  theme: currentTheme,
})
