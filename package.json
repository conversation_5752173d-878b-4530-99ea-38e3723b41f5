{"name": "uni-vue3", "type": "commonjs", "version": "1.0.0", "description": "uni-vue3 基于vue3+vite5+ts+unocss搭建", "engines": {"node": ">=18", "pnpm": ">=7.30"}, "scripts": {"upgrade": "npx @dcloudio/uvm@latest", "test:h5": "uni --mode test", "test:h5-didi": "uni -p h5-didi    --mode test", "wxtest": "uni -p mp-weixin --mode test", "locality:h5": "uni --mode locality", "production:h5": "uni --mode production", "dev:mp-alipay": "uni -p mp-alipay", "test:mp-weixin": "uni -p mp-weixin    --mode test", "prepare": "git init && husky install && node -e \"try { require('fs').chmodSync('.husky/commit-msg', 0o755); require('fs').chmodSync('.husky/pre-commit', 0o755); } catch (e) {}\"", "type-check": "vue-tsc --noEmit", "f2buildeDev": "uni build -p h5-didi --outDir dist/build  --mode development", "f2eDev": "uni build -p h5-didi --outDir dist  --mode development ", "f2eTest": "uni build -p h5-didi --outDir dist --mode test ", "f2ePub": "uni build -p h5-didi --outDir dist --mode production ", "f2eDevApp": "uni build -p h5 --outDir dist/development/1000000599  --mode development && node build.js development 1000000599 zh", "f2eTestApp": "uni build -p h5 --outDir dist/test/1000000599  --mode test && node build.js test 1000000599 zh", "f2ePubApp": "uni build -p h5 --outDir dist/production/1000000599     --mode production && node build.js production 1000000599 zh", "f2eWxTest": "uni build -p  mp-weixin   --outDir dist/wxTest  --mode test   --subpackage=subPackageCommon && node wxBuild.js dist/wxTest/subPackageCommon &&  node ./oss/rsdp.js --env wxTest --name subPackageCommon", "f2eWxPub": "uni build -p  mp-weixin    --outDir dist/wxProduction --mode production   --subpackage=subPackageCommon && node wxBuild.js dist/wxProduction/subPackageCommon &&  node ./oss/rsdp.js --env wxProduction --name subPackageCommon", "testNode": "node  wxBuild.js dist/wxTest/subPackageCommon"}, "lint-staged": {"**/*.{html,vue,ts,cjs,json,md}": ["prettier --write"], "**/*.{vue,js,ts,jsx,tsx}": ["eslint --fix"], "**/*.{vue,css,scss,html}": ["stylelint --fix"]}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4040520250104002", "@dcloudio/uni-components": "3.0.0-4040520250104002", "@dcloudio/uni-h5": "3.0.0-4040520250104002", "@dcloudio/uni-mp-alipay": "3.0.0-4040520250104002", "@dcloudio/uni-mp-weixin": "3.0.0-4040520250104002", "@hbc/uni-helper": "^0.0.16", "crypto-js": "^4.2.0", "dayjs": "1.11.10", "jsencrypt": "^3.3.2", "luch-request": "^3.1.1", "nutui-uniapp": "^1.8.3", "pinia": "2.0.36", "pinia-plugin-persistedstate": "3.2.1", "qs": "6.5.3", "vue": "3.4.21"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@dcloudio/types": "^3.4.14", "@dcloudio/uni-automator": "3.0.0-4040520250104002", "@dcloudio/uni-cli-shared": "3.0.0-4040520250104002", "@dcloudio/uni-stacktracey": "3.0.0-4040520250104002", "@dcloudio/vite-plugin-uni": "3.0.0-4040520250104002", "@esbuild/darwin-arm64": "0.20.2", "@esbuild/darwin-x64": "0.20.2", "@iconify-json/ri": "^1.2.5", "@rollup/rollup-darwin-x64": "^4.34.8", "@types/node": "^20.17.19", "@types/wechat-miniprogram": "^3.4.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@uni-helper/vite-plugin-uni-layouts": "^0.1.10", "@uni-helper/vite-plugin-uni-manifest": "^0.2.7", "@uni-helper/vite-plugin-uni-platform": "^0.0.4", "@unocss/preset-legacy-compat": "^0.59.4", "@vue/runtime-core": "^3.5.13", "@vue/tsconfig": "^0.1.3", "ali-oss": "^6.1.1", "autoprefixer": "^10.4.20", "axios": "^0.24.0", "big.js": "^6.2.2", "co": "^4.6.0", "commitlint": "^18.6.1", "compressing": "^1.10.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.1.0", "eslint-import-resolver-typescript": "^3.8.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^9.32.0", "form-data": "^4.0.0", "husky": "^8.0.3", "less": "^4.2.2", "lint-staged": "^15.4.3", "postcss": "^8.5.3", "postcss-html": "^1.8.0", "postcss-scss": "^4.0.9", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.85.1", "stylelint": "^16.14.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended": "^14.0.1", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-prettier": "^5.0.3", "terser": "^5.39.0", "typescript": "^4.9.5", "unocss": "^0.58.9", "unocss-applet": "^0.7.8", "unplugin-auto-import": "^0.17.8", "vite": "5.2.8", "vite-plugin-restart": "^0.4.2", "vue-tsc": "^1.8.27"}, "packageManager": "pnpm@8.3.1+sha1.75c6e8a4075abfc494770f998bf37b9ada110f51", "uni-app": {"scripts": {"h5-didi": {"title": "滴滴H5", "browser": "", "env": {"UNI_PLATFORM": "h5", "PLATFORM": "didi"}, "define": {"H5-DIDI": true}}, "h5-hbc": {"title": "皇包车离线包H5", "browser": "", "env": {"UNI_PLATFORM": "h5", "PLATFORM": "hbc"}, "define": {"H5-HBC": true}}}}}