const process = require("process")
const path = require("path")
const fs = require("fs")
const { zip } = require("compressing")
// 读取args node ./oss/rsdp.js --env wxTest --name subPackageTravel
const args = process.argv.slice(2) // 跳过 node 和脚本路径
const options = args.reduce((acc, arg, i) => {
  if (arg.startsWith("--")) {
    const next = args[i + 1]
    if (next && !next.startsWith("--")) {
      acc[arg.slice(2)] = next
    }
  }
  return acc
}, {})
console.log(options)

// 将subPackageTravel.json 复制到 dist/wxTest/subPackageTravel
const subPackageTravelJsonPath = path.join(process.cwd(), "subPackageTravel.json")
const distPath = path.join(process.cwd(), `dist/${options.env}/${options.name}`)
const distSubPackageTravelJsonPath = path.join(distPath, "subPackageTravel.json")
fs.copyFileSync(subPackageTravelJsonPath, distSubPackageTravelJsonPath)

const projectStaticPath = path.join(process.cwd(), `dist/${options.env}/${options.name}`)

//压缩 projectStaticPath
const zipPath = path.join(process.cwd(), `dist/${options.env}/${options.name}.zip`)
zip.compressDir(projectStaticPath, zipPath).then((res) => {
  if (!res) {
    const Deploy = require("./deploy.js")
    new Deploy(zipPath, `wxMiniApp/${options.env}`).uploadAllFile()
  }
})
