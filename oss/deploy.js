/* eslint-disable */
const OSS = require("ali-oss")
const co = require("co")
const fs = require("fs")
const path = require("path")
const chalk = require("chalk")

const client = new OSS({
  region: "oss-cn-hangzhou",
  accessKeyId: "LTAI5tR5qtwrDajQH5Xtafxi",
  accessKeySecret: "******************************",
  bucket: "fr-static-new",
  timeout: 5000000,
})
class Deploy {
  /**
   * 构造函数
   * @param {string} zipFilePath zip文件的路径
   * @param {string} dist 阿里云OSS目标路径
   */
  constructor(zipFilePath, dist) {
    this.zipFilePath = path.resolve(zipFilePath)
    this.dist = dist
    this.count = 0
    this.fail = 0
    this.length = 1 // 只有一个文件
  }

  /**
   * 获取zip文件信息
   * @returns {Array} 包含zip文件信息的数组
   */
  getZipFile() {
    // 检查文件是否存在
    if (!fs.existsSync(this.zipFilePath)) {
      console.log(chalk.red(`错误: 文件 ${this.zipFilePath} 不存在`))
      return []
    }

    // 检查是否为zip文件
    if (!this.zipFilePath.toLowerCase().endsWith(".zip")) {
      console.log(chalk.red(`错误: 文件 ${this.zipFilePath} 不是zip文件`))
      return []
    }

    // 获取文件名
    const fileName = path.basename(this.zipFilePath)

    // 创建文件对象
    const fileObj = {
      path: this.zipFilePath, // 本地路径
      filename: `${this.dist}/${fileName}`, // OSS路径
    }

    return [fileObj]
  }

  /**
   * 获取要上传的文件列表
   */
  get allFiles() {
    return this.getZipFile()
  }
  uploadFile(
    fileObj = {
      filename: "",
      path: "",
    },
  ) {
    const { filename, path } = fileObj
    const self = this
    co(function* () {
      const result = yield client.put(filename, path)
      if (result.res.status === 200) {
        self.count++
        console.log(
          chalk.green(
            `成功${self.count}：${result.url.replace(/http:\/\/fr-static\.oss-cn-hangzhou\.aliyuncs\.com/, "https://fr-static-new.oss-cn-hangzhou.aliyuncs.com")}`,
          ),
        )
      } else {
        self.fail++
        console.log(chalk.red(`失败${self.fail}: ${path}失败`))
      }
    })
      .then(() => {
        if (self.count === self.length) {
          console.log(chalk.green("文件已全部上传！！！"))
        }
        if (self.fail > 0) {
          console.log(chalk.red(`共有${self.fail}个文件上传失败，请重新上传！！！！`))
        }
      })
      .catch((err) => {
        console.log(chalk.red(err))
      })
  }
  /**
   * 上传zip文件
   * @param {Array} arr 要上传的文件列表
   */
  uploadAllFile(arr = this.allFiles) {
    if (arr.length === 0) {
      console.log(chalk.red("没有找到有效的zip文件，上传终止"))
      return
    }

    const self = this
    this.length = arr.length
    console.log(chalk.blue(`开始上传zip文件 from ${this.zipFilePath} to ${this.dist}`))

    // 上传文件
    arr.forEach((item) => {
      self.uploadFile(item)
    })
  }
}

module.exports = Deploy
