const chalk = require('chalk');
const process = require('child_process');

const { exec } = process;
const cmd = 'git fetch origin master && git rev-list --left-right --count origin/master...@ | cut -f1';
exec(cmd, (err, stdout) => {
  if (err) {
    throw new Error(err);
  }
  if (stdout.replace('\n', '') === '0') {
    console.log('[git] Current branch is update to master'.green);
    // process.exit(1);
  } else {
    console.warn(chalk.red('========================git merge ==========================='));
    console.log(chalk.red('[git !important] master had been updated ! please merge master into current branch'));
    console.warn(chalk.red('========================git merge ==========================='));
    throw new Error('');
  }
});
