const fs = require("fs")
const path = require("path")

// 读取pages.json文件
const pagesJsonPath = path.join(__dirname, "src/pages.json")
const pagesJsonContent = fs.readFileSync(pagesJsonPath, "utf8")

// 使用正则表达式移除注释
const cleanContent = pagesJsonContent.replace(/\/\*[\s\S]*?\*\/|\/\/[^\n]*|\/\/#[^\\]*$/gm, "")

// 解析JSON
const pagesJson = JSON.parse(cleanContent)

// 提取pages中的path
const pages = pagesJson.pages.map((page) => {
  if (typeof page === "string") {
    return page
  } else if (typeof page === "object" && page.path) {
    return page.path
  }
  return ""
})

// 提取subPackages中的path并拼接root
const subPackages = []
pagesJson.subPackages.forEach((subPackage) => {
  const root = subPackage.root || ""
  subPackage.pages.forEach((page) => {
    let pagePath
    if (typeof page === "string") {
      pagePath = page
    } else if (typeof page === "object" && page.path) {
      pagePath = page.path
    } else {
      return
    }
    // 拼接root和path
    const fullPath = root ? `${root}/${pagePath}` : pagePath
    subPackages.push(fullPath)
  })
})

const allPages = [...pages, ...subPackages]
// 读取 命令上的参数
const realPath = process.argv[2]
allPages.forEach(async (res) => {
  const wxssPath = path.join(realPath, `${res}.wxss`)
  try {
    // 读取wxss文件内容
    const wxssContent = await fs.promises.readFile(wxssPath, "utf8")

    // 检查是否已经包含@import语句
    if (!wxssContent.includes("@import '../../app.wxss'")) {
      // 添加@import语句
      const newContent = `@import '../../app.wxss';\n${wxssContent}`
      await fs.promises.writeFile(wxssPath, newContent, "utf8")
      console.log(`已向 ${wxssPath} 添加 @import 语句`)
    } else {
      console.log(`跳过 ${wxssPath}，已包含 @import 语句`)
    }
  } catch (error) {
    console.error(`处理 ${wxssPath} 时出错:`, error)
  }
})
